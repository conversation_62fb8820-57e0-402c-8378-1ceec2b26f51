# API Documentation

## Overview

The Smart Core API provides RESTful endpoints for team management, usage tracking, and analytics. All API endpoints require authentication via Laravel Sanctum tokens.

## Authentication

### Token Generation

```http
POST /api/auth/token
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password",
    "device_name": "Mobile App"
}
```

**Response:**
```json
{
    "token": "1|abc123...",
    "user": {
        "id": 1,
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "type": "team_user"
    },
    "team": {
        "id": 1,
        "name": "Acme Corp",
        "status": "active"
    }
}
```

### Using Tokens

Include the token in the Authorization header:

```http
Authorization: Bearer 1|abc123...
```

## Base URL

```
https://your-domain.com/api
```

## Endpoints

### Team Information

#### Get Current Team
```http
GET /api/team
Authorization: Bearer {token}
```

**Response:**
```json
{
    "data": {
        "id": 1,
        "name": "Acme Corp",
        "status": "active",
        "current_plan": {
            "id": 2,
            "name": "Plus",
            "monthly_credits": 1000,
            "additional_credits": 100,
            "monthly_usage": 250,
            "remaining_credits": 850,
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "payment_status": "paid"
        },
        "settings": {
            "timezone": "Asia/Colombo",
            "notifications": true
        }
    }
}
```

### Usage Tracking

#### Track Usage
```http
POST /api/usage
Authorization: Bearer {token}
Content-Type: application/json

{
    "action": "document_processing",
    "credits": 5,
    "metadata": {
        "document_type": "pdf",
        "pages": 10,
        "file_size": 2048576
    }
}
```

**Response:**
```json
{
    "success": true,
    "transaction_id": 123,
    "remaining_credits": 845,
    "message": "Usage tracked successfully"
}
```

#### Get Usage History
```http
GET /api/usage?page=1&per_page=50&from=2024-01-01&to=2024-01-31
Authorization: Bearer {token}
```

**Response:**
```json
{
    "data": [
        {
            "id": 123,
            "type": "usage",
            "credits": -5,
            "description": "document_processing",
            "metadata": {
                "document_type": "pdf",
                "pages": 10
            },
            "created_at": "2024-01-15T10:30:00Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "per_page": 50,
        "total": 150,
        "last_page": 3
    }
}
```

### Statistics

#### Get Usage Statistics
```http
GET /api/stats?period=30days
Authorization: Bearer {token}
```

**Response:**
```json
{
    "data": {
        "period": "30days",
        "total_usage": 250,
        "daily_average": 8.3,
        "peak_day": {
            "date": "2024-01-15",
            "usage": 45
        },
        "usage_by_action": {
            "document_processing": 180,
            "api_calls": 70
        },
        "daily_breakdown": [
            {
                "date": "2024-01-01",
                "usage": 12
            },
            {
                "date": "2024-01-02",
                "usage": 8
            }
        ]
    }
}
```

## Error Responses

### Standard Error Format

```json
{
    "error": "Error message",
    "code": "ERROR_CODE",
    "details": {
        "field": ["Validation error message"]
    }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `UNAUTHENTICATED` | 401 | Invalid or missing token |
| `INSUFFICIENT_CREDITS` | 402 | Not enough credits for operation |
| `FORBIDDEN` | 403 | Access denied |
| `VALIDATION_ERROR` | 422 | Invalid input data |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Server error |

### Example Error Responses

#### Insufficient Credits
```json
{
    "error": "Insufficient credits for this operation",
    "code": "INSUFFICIENT_CREDITS",
    "details": {
        "required": 10,
        "available": 5,
        "team_id": 1
    }
}
```

#### Validation Error
```json
{
    "error": "The given data was invalid",
    "code": "VALIDATION_ERROR",
    "details": {
        "credits": ["The credits field must be a positive integer"],
        "action": ["The action field is required"]
    }
}
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **General endpoints**: 60 requests per minute
- **Usage tracking**: 100 requests per minute
- **Authentication**: 5 requests per minute

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

## Webhooks (Future Feature)

### Webhook Events

- `usage.recorded` - When usage is tracked
- `credits.low` - When credits fall below threshold
- `plan.expired` - When plan expires
- `payment.failed` - When payment fails

### Webhook Payload Example

```json
{
    "event": "usage.recorded",
    "data": {
        "team_id": 1,
        "user_id": 5,
        "transaction_id": 123,
        "credits_used": 5,
        "remaining_credits": 845,
        "action": "document_processing"
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
```

## SDK Examples

### JavaScript/Node.js

```javascript
class SmartCoreAPI {
    constructor(token, baseURL = 'https://your-domain.com/api') {
        this.token = token;
        this.baseURL = baseURL;
    }
    
    async trackUsage(action, credits, metadata = {}) {
        const response = await fetch(`${this.baseURL}/usage`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action, credits, metadata })
        });
        
        return response.json();
    }
    
    async getTeamInfo() {
        const response = await fetch(`${this.baseURL}/team`, {
            headers: {
                'Authorization': `Bearer ${this.token}`,
            }
        });
        
        return response.json();
    }
}

// Usage
const api = new SmartCoreAPI('your-token-here');
const result = await api.trackUsage('document_processing', 5, {
    document_type: 'pdf',
    pages: 10
});
```

### PHP

```php
class SmartCoreAPI
{
    private $token;
    private $baseURL;
    
    public function __construct($token, $baseURL = 'https://your-domain.com/api')
    {
        $this->token = $token;
        $this->baseURL = $baseURL;
    }
    
    public function trackUsage($action, $credits, $metadata = [])
    {
        $response = Http::withToken($this->token)
            ->post("{$this->baseURL}/usage", [
                'action' => $action,
                'credits' => $credits,
                'metadata' => $metadata,
            ]);
            
        return $response->json();
    }
    
    public function getTeamInfo()
    {
        $response = Http::withToken($this->token)
            ->get("{$this->baseURL}/team");
            
        return $response->json();
    }
}

// Usage
$api = new SmartCoreAPI('your-token-here');
$result = $api->trackUsage('document_processing', 5, [
    'document_type' => 'pdf',
    'pages' => 10,
]);
```

### Python

```python
import requests

class SmartCoreAPI:
    def __init__(self, token, base_url='https://your-domain.com/api'):
        self.token = token
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def track_usage(self, action, credits, metadata=None):
        data = {
            'action': action,
            'credits': credits,
            'metadata': metadata or {}
        }
        response = requests.post(
            f'{self.base_url}/usage',
            json=data,
            headers=self.headers
        )
        return response.json()
    
    def get_team_info(self):
        response = requests.get(
            f'{self.base_url}/team',
            headers=self.headers
        )
        return response.json()

# Usage
api = SmartCoreAPI('your-token-here')
result = api.track_usage('document_processing', 5, {
    'document_type': 'pdf',
    'pages': 10
})
```

## Testing

### Test Endpoints

For development and testing, use the sandbox environment:

```
Base URL: https://sandbox.your-domain.com/api
```

### Test Data

Test tokens and teams are available in the sandbox environment for integration testing.

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.your-domain.com
- Status Page: https://status.your-domain.com

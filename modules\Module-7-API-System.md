# Module 7: API System

## Overview
Implement a comprehensive RESTful API system with token authentication, usage tracking, and proper documentation. This module enables external integrations and programmatic access to the SaaS platform.

## Module Goals
- Configure Laravel Sanctum for API authentication
- Create API controllers for core functionality
- Implement API resources for consistent responses
- Setup API routes with proper middleware
- Create comprehensive API testing

## Tasks

### Task 39: Configure Sanctum
**Status**: [ ] Not Started  
**Goal**: Setup API authentication with Laravel Sanctum

#### Requirements
- [ ] Update Sanctum configuration for API needs
- [ ] Set token expiration policies
- [ ] Configure CORS for API access
- [ ] Test API authentication flow

#### Files to update
- `config/sanctum.php`
- `config/cors.php`

#### Sanctum Configuration
```php
// config/sanctum.php
'expiration' => null, // Never expire tokens
'middleware' => [
    'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
    'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
],
```

#### CORS Configuration
```php
// config/cors.php
'paths' => ['api/*', 'sanctum/csrf-cookie'],
'allowed_methods' => ['*'],
'allowed_origins' => ['*'], // Configure for production
'allowed_headers' => ['*'],
'supports_credentials' => true,
```

#### Acceptance Criteria
- [ ] API authentication works correctly
- [ ] CORS is properly configured
- [ ] Token generation and validation work
- [ ] Configuration is production-ready

---

### Task 40: Create API Controllers
**Status**: [ ] Not Started  
**Goal**: Implement core API endpoints

#### Requirements
- [ ] Create ApiAuthController for authentication
- [ ] Create ApiTeamController for team operations
- [ ] Create ApiUsageController for usage tracking
- [ ] Create ApiStatsController for analytics
- [ ] Test all API controllers

#### Files to create
- `app/Http/Controllers/Api/ApiAuthController.php`
- `app/Http/Controllers/Api/ApiTeamController.php`
- `app/Http/Controllers/Api/ApiUsageController.php`
- `app/Http/Controllers/Api/ApiStatsController.php`

#### ApiAuthController Methods
```php
public function login(Request $request) // Generate API token
public function logout(Request $request) // Revoke current token
public function tokens(Request $request) // List user tokens
public function revokeToken(Request $request, $tokenId) // Revoke specific token
```

#### ApiTeamController Methods
```php
public function show(Request $request) // Get current team info
public function members(Request $request) // Get team members
public function usage(Request $request) // Get team usage stats
public function plan(Request $request) // Get current plan info
```

#### ApiUsageController Methods
```php
public function store(Request $request) // Track usage
public function index(Request $request) // Get usage history
public function stats(Request $request) // Get usage statistics
```

#### Acceptance Criteria
- [ ] All API endpoints work correctly
- [ ] Proper error handling implemented
- [ ] Authentication is enforced
- [ ] Response format is consistent

---

### Task 41: Create API Resources
**Status**: [ ] Not Started  
**Goal**: Standardize API response formatting

#### Requirements
- [ ] Create TeamResource for team data
- [ ] Create UserResource for user data
- [ ] Create CreditTransactionResource for transactions
- [ ] Create PlanResource for plan data
- [ ] Test API resource formatting

#### Files to create
- `app/Http/Resources/TeamResource.php`
- `app/Http/Resources/UserResource.php`
- `app/Http/Resources/CreditTransactionResource.php`
- `app/Http/Resources/PlanResource.php`
- `app/Http/Resources/TeamPlanResource.php`

#### Resource Examples
```php
// TeamResource
public function toArray($request)
{
    return [
        'id' => $this->id,
        'name' => $this->name,
        'status' => $this->status,
        'current_plan' => new TeamPlanResource($this->whenLoaded('currentPlan')),
        'members_count' => $this->users_count,
        'created_at' => $this->created_at,
        'settings' => $this->settings,
    ];
}
```

#### Acceptance Criteria
- [ ] Resources format data consistently
- [ ] Sensitive data is properly hidden
- [ ] Relationships are handled correctly
- [ ] Performance is optimized

---

### Task 42: Create API Routes
**Status**: [ ] Not Started  
**Goal**: Setup comprehensive API routing

#### Requirements
- [ ] Define API routes with proper versioning
- [ ] Add authentication middleware
- [ ] Add usage tracking middleware
- [ ] Group routes logically
- [ ] Test API routing

#### Files to update
- `routes/api.php`

#### API Route Structure
```php
// Authentication routes
Route::post('/auth/login', [ApiAuthController::class, 'login']);

Route::middleware('auth:sanctum')->group(function () {
    // Auth management
    Route::post('/auth/logout', [ApiAuthController::class, 'logout']);
    Route::get('/auth/tokens', [ApiAuthController::class, 'tokens']);
    Route::delete('/auth/tokens/{token}', [ApiAuthController::class, 'revokeToken']);
    
    // Team routes
    Route::get('/team', [ApiTeamController::class, 'show']);
    Route::get('/team/members', [ApiTeamController::class, 'members']);
    Route::get('/team/plan', [ApiTeamController::class, 'plan']);
    
    // Usage routes (with tracking middleware)
    Route::middleware('track.usage')->group(function () {
        Route::post('/usage', [ApiUsageController::class, 'store']);
        Route::get('/usage', [ApiUsageController::class, 'index']);
        Route::get('/usage/stats', [ApiUsageController::class, 'stats']);
    });
});
```

#### Acceptance Criteria
- [ ] All routes are properly protected
- [ ] Middleware is applied correctly
- [ ] Route naming is consistent
- [ ] Versioning strategy is implemented

---

### Task 43: Test API Module
**Status**: [ ] Not Started  
**Goal**: Ensure API functionality works correctly

#### Requirements
- [ ] Create API authentication tests
- [ ] Create API endpoint tests
- [ ] Create API usage tracking tests
- [ ] Test API error handling
- [ ] Performance test API endpoints

#### Files to create
- `tests/Feature/Api/ApiAuthTest.php`
- `tests/Feature/Api/ApiTeamTest.php`
- `tests/Feature/Api/ApiUsageTest.php`
- `tests/Feature/Api/ApiResourceTest.php`
- `tests/Feature/Api/ApiMiddlewareTest.php`

#### Test Coverage Areas
- Token authentication flow
- API endpoint functionality
- Usage tracking accuracy
- Error response format
- Rate limiting
- Resource formatting
- Middleware behavior

#### API Test Examples
```php
public function test_can_authenticate_and_get_token()
{
    $user = User::factory()->create();
    
    $response = $this->postJson('/api/auth/login', [
        'email' => $user->email,
        'password' => 'password',
        'device_name' => 'Test Device'
    ]);
    
    $response->assertStatus(200)
            ->assertJsonStructure(['token', 'user', 'team']);
}

public function test_can_track_usage_with_valid_token()
{
    $user = User::factory()->create();
    $team = Team::factory()->create();
    $user->teams()->attach($team);
    
    Sanctum::actingAs($user);
    
    $response = $this->postJson('/api/usage', [
        'action' => 'document_processing',
        'credits' => 5,
        'metadata' => ['type' => 'pdf']
    ]);
    
    $response->assertStatus(200)
            ->assertJson(['success' => true]);
}
```

#### Acceptance Criteria
- [ ] All API tests pass
- [ ] Test coverage >80% for API code
- [ ] Edge cases are covered
- [ ] Performance is acceptable
- [ ] Error scenarios are tested

---

## Module Completion Criteria

### Functional Requirements
- [ ] API authentication works correctly
- [ ] All API endpoints are functional
- [ ] Usage tracking is automatic
- [ ] API responses are consistent
- [ ] Error handling is comprehensive

### Technical Requirements
- [ ] Sanctum is properly configured
- [ ] Controllers handle requests correctly
- [ ] Resources format responses properly
- [ ] Routes are organized and protected
- [ ] Middleware functions correctly

### Quality Requirements
- [ ] Code follows Laravel best practices
- [ ] All code is properly tested
- [ ] API documentation is complete
- [ ] Performance is optimized
- [ ] Security is comprehensive

## Windows Development Notes
- Test API with Postman or similar tools
- Use Laravel Tinker for token testing
- Monitor API performance with debugging tools
- Test CORS configuration thoroughly

## Next Module
After completing all tasks in this module, proceed to **Module 8: Frontend & Landing**.

## Git Workflow
```bash
git checkout -b module-7-api-system
# Complete tasks...
git checkout main
git merge module-7-api-system
git tag v7.0-module-7
git push origin main --tags
```

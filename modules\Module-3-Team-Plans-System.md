# Module 3: Team Plans System

## Overview
Create the team-plan relationship system that links teams to subscription plans with monthly cycles, usage tracking, and payment management. This module connects teams to their billing plans.

## Module Goals
- Create team_plans table and model for team-plan relationships
- Implement team plan service for business logic
- Create team observer to auto-assign default plans
- Enhance teams table with status and settings
- Create plan validation middleware
- Establish comprehensive testing

## Tasks

### Task 13: Create Team Plans Database & Model
**Status**: [ ] Not Started  
**Goal**: Setup team-plan relationships with monthly cycles

#### Requirements
- [ ] Create team_plans table migration
- [ ] Create TeamPlan model with relationships
- [ ] Update Team model with plan relationships
- [ ] Create TeamPlanFactory for testing
- [ ] Test team-plan relationships

#### Files to create
- `database/migrations/2024_01_01_000003_create_team_plans_table.php`
- `app/Models/TeamPlan.php`
- `database/factories/TeamPlanFactory.php`
- Update `app/Models/Team.php`

#### Migration Schema
```php
Schema::create('team_plans', function (Blueprint $table) {
    $table->id();
    $table->foreignId('team_id')->constrained()->cascadeOnDelete();
    $table->foreignId('plan_id')->constrained()->cascadeOnDelete();
    $table->integer('monthly_credits');
    $table->integer('additional_credits')->default(0);
    $table->integer('monthly_usage')->default(0);
    $table->decimal('paid_amount', 10, 2)->default(0);
    $table->enum('payment_status', ['pending', 'paid', 'failed', 'cancelled'])->default('pending');
    $table->date('start_date');
    $table->date('end_date');
    $table->boolean('is_active')->default(true);
    $table->json('metadata')->nullable();
    $table->timestamps();
    
    $table->index(['team_id', 'start_date', 'end_date']);
    $table->index(['team_id', 'is_active']);
    $table->index(['payment_status', 'end_date']);
});
```

#### TeamPlan Model Methods
```php
public function team()
public function plan()
public function isActive(): bool
public function isExpired(): bool
public function getRemainingCredits(): int
public function getTotalCredits(): int
public function getUsagePercentage(): float
public function canUseCredits(int $credits): bool
public function scopeActive($query)
public function scopeCurrent($query)
```

#### Team Model Updates
```php
public function plans()
public function currentPlan()
public function getCurrentCredits(): int
public function hasActivePlan(): bool
public function getPlanHistory(): Collection
```

#### Acceptance Criteria
- [ ] Migration creates table with proper structure
- [ ] TeamPlan model has all required methods
- [ ] Team model relationships work correctly
- [ ] Factory creates realistic test data

---

### Task 14: Create Team Plan Service
**Status**: [ ] Not Started  
**Goal**: Implement team-plan business logic

#### Requirements
- [ ] Create TeamPlanService class
- [ ] Implement plan cycling logic
- [ ] Implement plan validation and management
- [ ] Handle plan upgrades and downgrades
- [ ] Test team plan service

#### Files to create
- `app/Services/TeamPlanService.php`

#### Service Methods
```php
public function ensureCurrentPlan(Team $team): TeamPlan
public function createMonthlyPlanCycle(Team $team, Plan $plan = null): TeamPlan
public function isValidPlan(Team $team): bool
public function getPlanHistory(Team $team): Collection
public function updatePaymentStatus(TeamPlan $teamPlan, string $status): void
public function addAdditionalCredits(Team $team, int $credits, string $description = null): void
public function upgradePlan(Team $team, Plan $newPlan): TeamPlan
public function downgradePlan(Team $team, Plan $newPlan): TeamPlan
public function cancelPlan(Team $team): void
public function renewPlan(Team $team): TeamPlan
```

#### Business Logic
- Ensure only one active plan per team
- Handle plan transitions properly
- Manage credit allocations and usage
- Track payment status and history
- Handle plan expiration and renewal

#### Acceptance Criteria
- [ ] Service handles all team-plan operations
- [ ] Business rules are enforced correctly
- [ ] Plan transitions work smoothly
- [ ] All service methods are tested

---

### Task 15: Create Team Observer
**Status**: [ ] Not Started  
**Goal**: Auto-assign default plan to new teams

#### Requirements
- [ ] Create TeamObserver
- [ ] Implement plan assignment on team creation
- [ ] Handle team status changes
- [ ] Register observer in EventServiceProvider
- [ ] Test team plan assignment

#### Files to create
- `app/Observers/TeamObserver.php`
- Update `app/Providers/EventServiceProvider.php`

#### Observer Methods
```php
public function created(Team $team)
public function updated(Team $team)
public function deleting(Team $team)
```

#### Observer Logic
```php
// On team creation
public function created(Team $team)
{
    $defaultPlan = app(PlanService::class)->getDefaultPlan();
    app(TeamPlanService::class)->createMonthlyPlanCycle($team, $defaultPlan);
}
```

#### Acceptance Criteria
- [ ] Observer assigns default plan to new teams
- [ ] Observer handles team events properly
- [ ] Observer is properly registered
- [ ] All observer methods are tested

---

### Task 16: Update Teams Database
**Status**: [ ] Not Started  
**Goal**: Enhance teams table with status and settings

#### Requirements
- [ ] Create migration to add status column to teams
- [ ] Add settings JSON column for team configuration
- [ ] Add last_activity_at for tracking
- [ ] Update Team model with new functionality
- [ ] Test team status functionality

#### Files to create
- `database/migrations/2024_01_01_000004_add_fields_to_teams_table.php`
- Update `app/Models/Team.php`

#### Migration Schema
```php
Schema::table('teams', function (Blueprint $table) {
    $table->enum('status', ['active', 'suspended', 'cancelled'])->default('active')->after('personal_team');
    $table->json('settings')->nullable()->after('status');
    $table->timestamp('last_activity_at')->nullable()->after('settings');
    
    $table->index('status');
    $table->index('last_activity_at');
});
```

#### Team Model Updates
```php
public function isActive(): bool
public function isSuspended(): bool
public function isCancelled(): bool
public function getSetting(string $key, $default = null)
public function setSetting(string $key, $value): void
public function updateLastActivity(): void
public function scopeActive($query)
public function scopeStatus($query, string $status)
```

#### Acceptance Criteria
- [ ] Migration adds fields successfully
- [ ] Team status methods work correctly
- [ ] Settings management is functional
- [ ] Activity tracking works properly

---

### Task 17: Create Team Plan Middleware
**Status**: [ ] Not Started  
**Goal**: Ensure valid team plans for protected routes

#### Requirements
- [ ] Create EnsureValidPlan middleware
- [ ] Create EnsureActiveTeam middleware
- [ ] Test plan validation middleware
- [ ] Register middleware in Kernel
- [ ] Handle different response types (web/API)

#### Files to create
- `app/Http/Middleware/EnsureValidPlan.php`
- `app/Http/Middleware/EnsureActiveTeam.php`
- Update `app/Http/Kernel.php`

#### Middleware Logic
```php
// EnsureValidPlan
public function handle(Request $request, Closure $next)
{
    $team = $request->user()?->currentTeam;
    
    if (!$team || !app(TeamPlanService::class)->isValidPlan($team)) {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Invalid or expired plan',
                'code' => 'INVALID_PLAN'
            ], 402);
        }
        
        return redirect()->route('team.billing')->with('error', 'Please update your plan');
    }
    
    return $next($request);
}

// EnsureActiveTeam
public function handle(Request $request, Closure $next)
{
    $team = $request->user()?->currentTeam;
    
    if (!$team || !$team->isActive()) {
        abort(403, 'Team is not active');
    }
    
    return $next($request);
}
```

#### Acceptance Criteria
- [ ] Middleware correctly validates plans
- [ ] Proper error responses for web and API
- [ ] Middleware is registered correctly
- [ ] All middleware scenarios are tested

---

### Task 18: Test Team Plans Module
**Status**: [ ] Not Started  
**Goal**: Ensure team-plan system works correctly

#### Requirements
- [ ] Create team plan model tests
- [ ] Create team plan service tests
- [ ] Create team observer tests
- [ ] Create middleware tests
- [ ] Test complete team-plan workflow

#### Files to create
- `tests/Unit/Models/TeamPlanTest.php`
- `tests/Unit/Services/TeamPlanServiceTest.php`
- `tests/Unit/Observers/TeamObserverTest.php`
- `tests/Feature/Middleware/TeamPlanMiddlewareTest.php`
- `tests/Feature/Team/TeamPlanAssignmentTest.php`

#### Test Coverage Areas
- TeamPlan model methods and relationships
- Team model plan-related methods
- TeamPlanService business logic
- Team observer event handling
- Middleware plan validation
- Plan assignment and cycling
- Credit management and usage

#### Integration Tests
- Complete team creation to plan assignment flow
- Plan upgrade/downgrade workflows
- Plan expiration and renewal
- Credit usage and validation

#### Acceptance Criteria
- [ ] All tests pass
- [ ] Test coverage >80% for team-plan code
- [ ] Edge cases are covered
- [ ] Integration workflows are tested

---

## Module Completion Criteria

### Functional Requirements
- [ ] Team-plan relationships working correctly
- [ ] Team plan service handling business logic properly
- [ ] Team observer assigning default plans automatically
- [ ] Team status and settings management functional
- [ ] Plan validation middleware protecting routes
- [ ] All team-plan tests passing

### Technical Requirements
- [ ] Database migrations run without errors
- [ ] TeamPlan model methods work as expected
- [ ] Team model plan relationships functional
- [ ] Service layer implements business rules correctly
- [ ] Observer handles events properly
- [ ] Middleware validates plans correctly

### Quality Requirements
- [ ] Code follows Laravel best practices
- [ ] All code is properly tested
- [ ] Business logic is well-encapsulated
- [ ] No security vulnerabilities
- [ ] Performance is acceptable

## Windows Development Notes
- Ensure all migrations run in correct order
- Test team creation flow thoroughly
- Use Laravel Tinker for testing relationships
- Monitor database queries for performance

## Next Module
After completing all tasks in this module, proceed to **Module 4: Credit System**.

## Git Workflow
```bash
# Start module
git checkout -b module-3-team-plans-system

# After each task
git add .
git commit -m "Task X: [Description] - [What was accomplished]"

# After module completion
git checkout main
git merge module-3-team-plans-system
git tag v3.0-module-3
git push origin main --tags
```

# Smart Core SaaS Implementation Guide

## 📖 **Complete Reference for Building SaaS Platforms**

This comprehensive guide covers everything available in Smart Core and how to use it as a foundation for building any SaaS platform. Use this as your primary reference when implementing new SaaS products.

## 🏗️ **System Architecture Overview**

Smart Core provides a complete SaaS foundation with these core components:

### **1. Multi-Tenant Architecture**
- **Team-based isolation**: Each customer is a separate team
- **Shared database**: Efficient resource utilization with proper data isolation
- **Scalable design**: Supports thousands of teams on single instance

### **2. User Management System**
- **System-level access**: `system_admin` vs `team_user`
- **Team-level roles**: `admin` vs `user` within teams
- **Authentication**: Laravel Jetstream with Sanctum API tokens
- **User lifecycle**: Registration, activation, deactivation, deletion

### **3. Flexible Billing Engine**
- **Multiple billing cycles**: One-time, monthly, yearly
- **Credit-based usage**: Real-time tracking and consumption
- **Plan management**: Upgrades, downgrades, trials
- **Payment integration**: Ready for Stripe, PayPal, etc.

## 💳 **Billing System Implementation**

### **Available Plan Types**

#### **1. One-Time Plans (Perfect for Free Tiers)**
```php
// Example: Free trial with 50 credits
[
    'name' => 'Free Trial',
    'monthly_credits' => null,
    'one_time_credits' => 50,
    'billing_cycle' => 'one_time',
    'price' => 0,
]
```

**Use Cases:**
- Free trials for new users
- One-time purchase products
- Demo accounts
- Beta testing access

#### **2. Monthly Recurring Plans**
```php
// Example: Standard monthly subscription
[
    'name' => 'Pro Monthly',
    'monthly_credits' => 1000,
    'one_time_credits' => null,
    'billing_cycle' => 'monthly',
    'price' => 29.99,
]
```

**Use Cases:**
- Standard SaaS subscriptions
- Pay-as-you-go services
- Small to medium businesses

#### **3. Yearly Plans (with Discounts)**
```php
// Example: Annual subscription with discount
[
    'name' => 'Pro Yearly',
    'monthly_credits' => 1000,
    'billing_cycle' => 'yearly',
    'price' => 299.99, // 2 months free
]
```

**Use Cases:**
- Enterprise customers
- Cost-conscious users
- Long-term commitments

### **Credit System Usage**

#### **Tracking Usage**
```php
// Track any type of usage
$usageService = app(UsageService::class);
$result = $usageService->trackUsage($team, $user, 'api_call', 1, [
    'endpoint' => '/api/process-document',
    'file_size' => 1024000,
    'processing_time' => 2.5
]);
```

#### **Custom Credit Costs**
```php
// Different actions can cost different credits
$creditCosts = [
    'api_call' => 1,
    'document_processing' => 5,
    'ai_analysis' => 10,
    'video_processing' => 50,
    'bulk_export' => 25,
];
```

#### **Real-time Balance Checking**
```php
// Check if team has sufficient credits
$creditService = app(CreditService::class);
$balance = $creditService->getBalance($team);
$canProceed = $creditService->canUseCredits($team, $requiredCredits);
```

## 🔧 **Feature Management System**

### **Plan-based Features**
Control what features are available to each plan:

```php
// Plan feature configuration
'features' => [
    'api_access' => true,
    'webhooks' => false,
    'custom_integrations' => false,
    'white_label' => false,
    'sso' => false,
    'priority_support' => false,
    'analytics' => true,
    'export_data' => true,
]
```

### **Usage Limits per Plan**
```php
// Plan limits configuration
'limits' => [
    'max_projects' => 10,
    'max_team_members' => 5,
    'max_api_calls_per_minute' => 100,
    'max_storage_gb' => 10,
    'max_exports_per_day' => 50,
    'max_webhooks' => 5,
]
```

### **Checking Features in Code**
```php
// Check if team has access to a feature
if ($team->currentPlan->plan->hasFeature('webhooks')) {
    // Show webhook management interface
}

// Check usage limits
$limit = $team->currentPlan->plan->getLimit('max_projects');
if ($team->projects()->count() >= $limit) {
    // Show upgrade prompt
}
```

## 🔌 **API System Implementation**

### **Available API Endpoints**

#### **Authentication Endpoints**
```http
POST /api/auth/login          # Generate API token
POST /api/auth/logout         # Revoke current token
GET  /api/auth/tokens         # List user tokens
DELETE /api/auth/tokens/{id}  # Revoke specific token
```

#### **Team Management Endpoints**
```http
GET /api/team                 # Get current team info
GET /api/team/members         # Get team members
GET /api/team/plan            # Get current plan details
GET /api/team/usage           # Get usage statistics
```

#### **Usage Tracking Endpoints**
```http
POST /api/usage               # Track usage/consume credits
GET  /api/usage               # Get usage history
GET  /api/usage/stats         # Get usage analytics
GET  /api/usage/balance       # Get current credit balance
```

### **API Authentication**
```javascript
// Generate token
const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password',
        device_name: 'My App'
    })
});

const { token } = await response.json();

// Use token for API calls
const apiResponse = await fetch('/api/usage', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        action: 'document_processing',
        credits: 5
    })
});
```

### **Rate Limiting per Plan**
```php
// Automatic rate limiting based on plan
Route::middleware(['auth:sanctum', 'throttle:api'])->group(function () {
    // API routes automatically limited by plan's max_api_calls_per_minute
});
```

## 📊 **Analytics & Reporting System**

### **Built-in Analytics**

#### **Usage Analytics**
```php
// Get team usage statistics
$analytics = app(AnalyticsService::class);
$stats = $analytics->getUsageStatistics($team, '30days');

// Returns:
[
    'total_usage' => 1250,
    'daily_average' => 41.7,
    'peak_day' => ['date' => '2024-01-15', 'usage' => 89],
    'usage_by_action' => [
        'api_calls' => 800,
        'document_processing' => 300,
        'exports' => 150
    ],
    'daily_breakdown' => [...]
]
```

#### **Revenue Analytics**
```php
// Get revenue statistics
$revenue = $analytics->getRevenueStats('monthly');

// Returns:
[
    'total_revenue' => 15750.00,
    'monthly_recurring_revenue' => 12500.00,
    'new_customers' => 25,
    'churned_customers' => 3,
    'plan_distribution' => [...]
]
```

### **Custom Analytics Implementation**
```php
// Track custom events
Event::dispatch(new CustomAnalyticsEvent($team, 'feature_used', [
    'feature' => 'advanced_search',
    'user_id' => $user->id,
    'metadata' => ['search_terms' => 'laravel saas']
]));
```

## 🔔 **Webhook System**

### **Setting Up Webhooks**
```php
// Create webhook for team
$webhook = Webhook::create([
    'team_id' => $team->id,
    'name' => 'Usage Notifications',
    'url' => 'https://customer-app.com/webhooks/usage',
    'events' => ['usage.recorded', 'credits.low', 'plan.changed'],
    'secret' => Str::random(32),
    'is_active' => true,
]);
```

### **Available Webhook Events**
```php
// Built-in webhook events
'usage.recorded'     // When credits are consumed
'credits.low'        // When credits fall below threshold
'plan.changed'       // When team changes plans
'plan.expired'       // When plan expires
'payment.successful' // When payment is processed
'payment.failed'     // When payment fails
'team.created'       // When new team is created
'user.invited'       // When user is invited to team
```

### **Webhook Payload Example**
```json
{
    "event": "usage.recorded",
    "timestamp": "2024-01-15T10:30:00Z",
    "team_id": 123,
    "data": {
        "user_id": 456,
        "action": "document_processing",
        "credits_used": 5,
        "remaining_credits": 245,
        "metadata": {
            "file_type": "pdf",
            "file_size": 2048576
        }
    }
}
```

## 🛡️ **Security & Access Control**

### **Role-based Access Control**

#### **System-level Roles**
```php
// Check system admin access
if ($user->isSystemAdmin()) {
    // Access to admin panel, all teams, system settings
}

// Check team user access
if ($user->isTeamUser()) {
    // Access only to assigned teams
}
```

#### **Team-level Roles**
```php
// Check team admin access
if ($user->isTeamAdmin()) {
    // Can manage team members, settings, billing
}

// Check regular team user
if ($user->role === 'user') {
    // Can use team features but not manage
}
```

### **API Security**
```php
// API key management
$apiKey = ApiKey::create([
    'team_id' => $team->id,
    'user_id' => $user->id,
    'name' => 'Production API Key',
    'permissions' => ['usage.track', 'analytics.read'],
    'rate_limit_per_minute' => 1000,
    'expires_at' => now()->addYear(),
]);
```

### **Middleware Protection**
```php
// Protect routes with middleware
Route::middleware(['auth', 'admin'])->group(function () {
    // Admin-only routes
});

Route::middleware(['auth', 'team.admin'])->group(function () {
    // Team admin routes
});

Route::middleware(['auth', 'valid.plan'])->group(function () {
    // Routes requiring active plan
});
```

## 📈 **Quota Management System**

### **Setting Up Quotas**
```php
// Create usage quotas for team
UsageQuota::create([
    'team_id' => $team->id,
    'quota_type' => 'api_calls',
    'limit_value' => 10000,
    'reset_period' => 'monthly',
]);

UsageQuota::create([
    'team_id' => $team->id,
    'quota_type' => 'storage',
    'limit_value' => 10737418240, // 10GB in bytes
    'reset_period' => 'monthly',
]);
```

### **Checking Quotas**
```php
// Check if quota allows action
$quotaService = app(QuotaService::class);
$canProceed = $quotaService->checkQuota($team, 'api_calls', 1);

if (!$canProceed) {
    return response()->json([
        'error' => 'API call quota exceeded',
        'code' => 'QUOTA_EXCEEDED'
    ], 429);
}

// Track quota usage
$quotaService->incrementUsage($team, 'api_calls', 1);
```

## 🎨 **Customization & White-label**

### **Theme Customization**
```php
// Team-specific settings
$team->setSetting('theme', [
    'primary_color' => '#3B82F6',
    'secondary_color' => '#10B981',
    'logo_url' => 'https://cdn.example.com/logo.png',
    'custom_css' => '.header { background: #custom; }'
]);

// Check in views
@if($team->getSetting('theme.logo_url'))
    <img src="{{ $team->getSetting('theme.logo_url') }}" alt="Logo">
@endif
```

### **Custom Domains**
```php
// Support for custom domains
$team->setSetting('custom_domain', 'app.customer.com');

// Domain verification
$team->setSetting('domain_verified', true);
```

## 🔄 **Background Jobs & Automation**

### **Available Background Jobs**

#### **Plan Cycle Management**
```php
// Automatically create new plan cycles
CreateMonthlyPlanCycle::dispatch($team);

// Schedule in Kernel.php
$schedule->job(new CreateMonthlyPlanCycle)->monthlyOn(1, '00:00');
```

#### **Usage Reporting**
```php
// Generate usage reports
GenerateUsageReport::dispatch($team, 'monthly');

// Cleanup old data
CleanupOldData::dispatch();
```

#### **Notification Jobs**
```php
// Send low credit warnings
SendLowCreditWarning::dispatch($team);

// Send plan expiration notices
SendPlanExpirationNotice::dispatch($team);
```

## 📧 **Notification System**

### **Built-in Notifications**

#### **Credit Notifications**
```php
// Low credit warning
$user->notify(new LowCreditWarning($team, $remainingCredits));

// Credits exhausted
$user->notify(new CreditsExhausted($team));
```

#### **Plan Notifications**
```php
// Plan expiring soon
$user->notify(new PlanExpiring($team, $daysRemaining));

// Plan upgraded
$user->notify(new PlanUpgraded($team, $oldPlan, $newPlan));
```

#### **Team Notifications**
```php
// Welcome new user
$user->notify(new WelcomeUser($team));

// User invited to team
$invitedUser->notify(new TeamInvitation($team, $invitedBy));
```

### **Custom Notifications**
```php
// Create custom notification
class CustomFeatureNotification extends Notification
{
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('New Feature Available')
            ->line('A new feature is now available for your plan.')
            ->action('Try It Now', route('feature.new'))
            ->line('Thank you for using our service!');
    }
}

// Send notification
$user->notify(new CustomFeatureNotification());
```

## 🏢 **Building Different Types of SaaS**

### **1. API-First SaaS (like Stripe, Twilio)**

#### **Implementation Strategy**
```php
// Track API usage automatically
Route::middleware(['auth:sanctum', 'track.usage:api_call'])->group(function () {
    Route::post('/api/send-sms', [SmsController::class, 'send']);
    Route::post('/api/process-payment', [PaymentController::class, 'process']);
    Route::get('/api/analytics', [AnalyticsController::class, 'get']);
});

// Custom credit costs per endpoint
class SmsController extends Controller
{
    public function send(Request $request)
    {
        // SMS costs 2 credits
        if (!app(CreditService::class)->canUseCredits(auth()->user()->currentTeam, 2)) {
            return response()->json(['error' => 'Insufficient credits'], 402);
        }

        // Process SMS
        $result = $this->smsService->send($request->validated());

        // Track usage
        app(UsageService::class)->trackUsage(
            auth()->user()->currentTeam,
            auth()->user(),
            'sms_sent',
            2,
            ['to' => $request->to, 'message_length' => strlen($request->message)]
        );

        return response()->json($result);
    }
}
```

#### **Plan Structure for API SaaS**
```php
'plans' => [
    'developer' => [
        'name' => 'Developer',
        'monthly_credits' => 1000,
        'price' => 0,
        'features' => ['api_access' => true, 'webhooks' => false],
        'limits' => ['max_api_calls_per_minute' => 10]
    ],
    'startup' => [
        'name' => 'Startup',
        'monthly_credits' => 10000,
        'price' => 29,
        'features' => ['api_access' => true, 'webhooks' => true],
        'limits' => ['max_api_calls_per_minute' => 100]
    ],
    'business' => [
        'name' => 'Business',
        'monthly_credits' => 100000,
        'price' => 199,
        'features' => ['api_access' => true, 'webhooks' => true, 'priority_support' => true],
        'limits' => ['max_api_calls_per_minute' => 1000]
    ]
]
```

### **2. Analytics SaaS (like Google Analytics)**

#### **Implementation Strategy**
```php
// Track data collection
class AnalyticsController extends Controller
{
    public function track(Request $request)
    {
        // Each event costs 0.1 credits
        $credits = 0.1;

        if (!app(CreditService::class)->canUseCredits(auth()->user()->currentTeam, $credits)) {
            return response()->json(['error' => 'Insufficient credits'], 402);
        }

        // Store analytics data
        AnalyticsEvent::create([
            'team_id' => auth()->user()->currentTeam->id,
            'event_type' => $request->event,
            'properties' => $request->properties,
            'timestamp' => now(),
        ]);

        // Track usage
        app(UsageService::class)->trackUsage(
            auth()->user()->currentTeam,
            auth()->user(),
            'event_tracked',
            $credits,
            ['event_type' => $request->event]
        );

        return response()->json(['status' => 'tracked']);
    }
}
```

#### **Plan Structure for Analytics SaaS**
```php
'plans' => [
    'free' => [
        'name' => 'Free',
        'one_time_credits' => 1000, // 10,000 events
        'billing_cycle' => 'one_time',
        'price' => 0,
        'limits' => ['max_websites' => 1, 'data_retention_days' => 30]
    ],
    'pro' => [
        'name' => 'Pro',
        'monthly_credits' => 10000, // 100,000 events
        'price' => 49,
        'limits' => ['max_websites' => 10, 'data_retention_days' => 365]
    ]
]
```

### **3. Content Management SaaS (like WordPress.com)**

#### **Implementation Strategy**
```php
// Track content operations
class ContentController extends Controller
{
    public function store(Request $request)
    {
        // Check storage quota
        $team = auth()->user()->currentTeam;
        $storageUsed = $team->getStorageUsed();
        $storageLimit = $team->currentPlan->plan->getLimit('max_storage_gb') * 1024 * 1024 * 1024;

        if ($storageUsed >= $storageLimit) {
            return response()->json(['error' => 'Storage quota exceeded'], 402);
        }

        // Create content
        $content = Content::create([
            'team_id' => $team->id,
            'title' => $request->title,
            'body' => $request->body,
            'size' => strlen($request->body),
        ]);

        // Track usage
        app(UsageService::class)->trackUsage($team, auth()->user(), 'content_created', 1);

        return response()->json($content);
    }
}
```

### **4. Communication SaaS (like Slack)**

#### **Implementation Strategy**
```php
// Track message sending
class MessageController extends Controller
{
    public function send(Request $request)
    {
        $team = auth()->user()->currentTeam;

        // Check team member limit
        $memberLimit = $team->currentPlan->plan->getLimit('max_team_members');
        if ($team->users()->count() >= $memberLimit) {
            return response()->json(['error' => 'Team member limit reached'], 402);
        }

        // Send message (costs 0.1 credits)
        $message = Message::create([
            'team_id' => $team->id,
            'user_id' => auth()->id(),
            'channel_id' => $request->channel_id,
            'content' => $request->content,
        ]);

        // Track usage
        app(UsageService::class)->trackUsage($team, auth()->user(), 'message_sent', 0.1);

        return response()->json($message);
    }
}
```

## 🔧 **Advanced Customization**

### **Custom Service Integration**
```php
// Create custom service
class CustomSaasService
{
    protected $creditService;
    protected $usageService;

    public function __construct(CreditService $creditService, UsageService $usageService)
    {
        $this->creditService = $creditService;
        $this->usageService = $usageService;
    }

    public function processCustomAction(Team $team, User $user, array $data)
    {
        // Check if team can perform action
        if (!$this->creditService->canUseCredits($team, 5)) {
            throw new InsufficientCreditsException();
        }

        // Perform custom business logic
        $result = $this->performBusinessLogic($data);

        // Track usage
        $this->usageService->trackUsage($team, $user, 'custom_action', 5, [
            'action_type' => $data['type'],
            'result_size' => strlen($result),
        ]);

        return $result;
    }
}
```

### **Custom Plan Features**
```php
// Add custom features to plans
'features' => [
    'api_access' => true,
    'webhooks' => true,
    'custom_branding' => true,
    'advanced_analytics' => true,
    'priority_support' => true,

    // Your custom features
    'ai_processing' => true,
    'bulk_operations' => true,
    'custom_integrations' => true,
    'white_label_reports' => true,
]

// Check custom features
if ($team->currentPlan->plan->hasFeature('ai_processing')) {
    // Show AI processing options
}
```

### **Custom Limits and Quotas**
```php
// Add custom limits
'limits' => [
    'max_projects' => 100,
    'max_team_members' => 50,
    'max_api_calls_per_minute' => 1000,

    // Your custom limits
    'max_ai_requests_per_day' => 500,
    'max_file_size_mb' => 100,
    'max_concurrent_jobs' => 10,
    'max_custom_domains' => 5,
]

// Enforce custom limits
$aiRequestsToday = $team->getAiRequestsToday();
$limit = $team->currentPlan->plan->getLimit('max_ai_requests_per_day');

if ($aiRequestsToday >= $limit) {
    return response()->json(['error' => 'Daily AI request limit exceeded'], 429);
}
```

## 📊 **Custom Analytics Implementation**

### **Track Custom Metrics**
```php
// Custom analytics service
class CustomAnalyticsService
{
    public function trackCustomEvent(Team $team, string $event, array $properties = [])
    {
        CustomAnalytic::create([
            'team_id' => $team->id,
            'event' => $event,
            'properties' => $properties,
            'timestamp' => now(),
        ]);
    }

    public function getCustomStats(Team $team, string $period = '30days')
    {
        $startDate = now()->sub($period);

        return CustomAnalytic::where('team_id', $team->id)
            ->where('created_at', '>=', $startDate)
            ->groupBy('event')
            ->selectRaw('event, count(*) as count')
            ->get();
    }
}
```

### **Custom Dashboard Widgets**
```php
// Create custom dashboard widgets
class CustomDashboardController extends Controller
{
    public function getWidgetData(Request $request)
    {
        $team = auth()->user()->currentTeam;

        return response()->json([
            'total_projects' => $team->projects()->count(),
            'active_users' => $team->getActiveUsersCount(),
            'monthly_usage' => $team->getMonthlyUsage(),
            'custom_metric' => $team->getCustomMetric(),
        ]);
    }
}
```

## 🚀 **Deployment & Scaling**

### **Environment Configuration**
```env
# Production environment
APP_ENV=production
APP_DEBUG=false

# Database
DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_DATABASE=smart_core_production

# Cache & Queue
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis

# Smart Core Configuration
SMART_CORE_DEFAULT_PLAN=free
SMART_CORE_CREDIT_WARNING_THRESHOLD=10
SMART_CORE_AUTO_PLAN_CYCLING=true
```

### **Scaling Considerations**
```php
// Optimize for scale
class ScaledCreditService extends CreditService
{
    public function getBalance(Team $team): int
    {
        // Cache balance for 5 minutes
        return Cache::remember(
            "team_balance_{$team->id}",
            300,
            fn() => parent::getBalance($team)
        );
    }

    public function useCredits(Team $team, User $user, int $credits, string $description = null): bool
    {
        // Use database transactions for consistency
        return DB::transaction(function () use ($team, $user, $credits, $description) {
            return parent::useCredits($team, $user, $credits, $description);
        });
    }
}
```

## 📋 **Implementation Checklist**

### **Before Building Your SaaS**
- [ ] Define your credit cost structure
- [ ] Design your plan tiers and features
- [ ] Identify custom limits and quotas needed
- [ ] Plan your API endpoints
- [ ] Design your custom analytics requirements
- [ ] Choose your payment gateway integration
- [ ] Plan your webhook events
- [ ] Design your notification strategy

### **During Development**
- [ ] Implement custom controllers using Smart Core services
- [ ] Add custom middleware for your business logic
- [ ] Create custom notifications for your use case
- [ ] Implement custom analytics tracking
- [ ] Add custom plan features and limits
- [ ] Create custom dashboard widgets
- [ ] Implement webhook endpoints for integrations
- [ ] Add custom API endpoints

### **Before Launch**
- [ ] Test all credit tracking scenarios
- [ ] Verify plan upgrade/downgrade flows
- [ ] Test API rate limiting
- [ ] Verify webhook delivery
- [ ] Test notification delivery
- [ ] Load test your custom endpoints
- [ ] Verify analytics accuracy
- [ ] Test payment integration

## 🎯 **Success Patterns**

### **Best Practices**
1. **Always check credits** before performing paid actions
2. **Track usage immediately** after successful operations
3. **Use webhooks** for real-time integrations
4. **Cache frequently accessed data** (balances, plan features)
5. **Implement proper error handling** for quota exceeded scenarios
6. **Use background jobs** for heavy operations
7. **Monitor usage patterns** to optimize pricing

### **Common Pitfalls to Avoid**
1. **Don't track usage** before validating the operation succeeded
2. **Don't forget to check quotas** in addition to credits
3. **Don't hardcode credit costs** - make them configurable
4. **Don't skip webhook signature verification**
5. **Don't ignore rate limiting** on your custom endpoints
6. **Don't forget to handle plan downgrades** gracefully

## 🎉 **You're Ready to Build!**

Smart Core provides everything you need to build a professional SaaS platform. Use this guide as your reference throughout development, and you'll have a robust, scalable SaaS product that can compete with industry leaders.

**Key Takeaways:**
- Smart Core handles all the complex SaaS infrastructure
- Focus on your unique business logic and user experience
- Leverage the built-in billing, analytics, and API systems
- Customize plans, features, and limits for your specific use case
- Use the webhook and notification systems for integrations
- Follow the scaling and security best practices

**Happy building!** 🚀
```

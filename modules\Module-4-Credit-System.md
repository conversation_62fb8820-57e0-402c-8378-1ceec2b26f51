# Module 4: Credit System

## Overview
Implement a comprehensive credit tracking and management system that logs all credit transactions, manages usage, and provides real-time balance calculations. This is the core billing mechanism of the SaaS system.

## Module Goals
- Create credit transactions table and model for detailed tracking
- Implement credit service for all credit operations
- Create usage service for unified tracking (API and web)
- Build admin credit management interface
- Create test usage endpoints for validation
- Establish comprehensive testing and audit trails

## Tasks

### Task 19: Create Credit Transactions Database & Model
**Status**: [ ] Not Started  
**Goal**: Setup comprehensive credit tracking system

#### Requirements
- [ ] Create credit_transactions table migration
- [ ] Create CreditTransaction model with relationships
- [ ] Create CreditTransactionFactory for testing
- [ ] Test credit transaction model functionality

#### Files to create
- `database/migrations/2024_01_01_000005_create_credit_transactions_table.php`
- `app/Models/CreditTransaction.php`
- `database/factories/CreditTransactionFactory.php`

#### Migration Schema
```php
Schema::create('credit_transactions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('team_id')->constrained()->cascadeOnDelete();
    $table->foreignId('user_id')->constrained()->cascadeOnDelete();
    $table->enum('type', ['usage', 'additional', 'refund', 'adjustment']);
    $table->integer('credits'); // Positive for additions, negative for usage
    $table->string('reference_type')->nullable(); // Polymorphic reference
    $table->unsignedBigInteger('reference_id')->nullable();
    $table->text('description')->nullable();
    $table->json('metadata')->nullable(); // Additional context data
    $table->timestamps();
    
    $table->index(['team_id', 'created_at']);
    $table->index(['user_id', 'created_at']);
    $table->index(['type', 'created_at']);
    $table->index(['reference_type', 'reference_id']);
});
```

#### CreditTransaction Model Methods
```php
public function team()
public function user()
public function reference() // Polymorphic relationship
public function isUsage(): bool
public function isAddition(): bool
public function getAbsoluteCredits(): int
public function scopeForTeam($query, Team $team)
public function scopeForUser($query, User $user)
public function scopeByType($query, string $type)
public function scopeInPeriod($query, $startDate, $endDate)
```

#### Acceptance Criteria
- [ ] Migration creates table with proper structure and indexes
- [ ] CreditTransaction model has all required methods
- [ ] Factory creates realistic transaction data
- [ ] Model relationships work correctly

---

### Task 20: Create Credit Service
**Status**: [ ] Not Started  
**Goal**: Implement comprehensive credit management logic

#### Requirements
- [ ] Create CreditService class
- [ ] Implement credit usage tracking
- [ ] Implement credit balance calculation
- [ ] Handle credit additions and adjustments
- [ ] Test credit service functionality

#### Files to create
- `app/Services/CreditService.php`

#### Service Methods
```php
public function useCredits(Team $team, User $user, int $credits, string $description = null, array $metadata = []): bool
public function addCredits(Team $team, User $user, int $credits, string $type = 'additional', string $description = null): void
public function adjustCredits(Team $team, User $user, int $credits, string $description): void
public function refundCredits(Team $team, User $user, int $credits, string $description): void
public function getBalance(Team $team): int
public function getUsageInPeriod(Team $team, $startDate, $endDate): int
public function getTransactionHistory(Team $team, int $limit = 50): Collection
public function logTransaction(Team $team, User $user, array $data): CreditTransaction
public function canUseCredits(Team $team, int $credits): bool
public function getUsageStatistics(Team $team, string $period = '30days'): array
```

#### Business Logic
```php
public function useCredits(Team $team, User $user, int $credits, string $description = null, array $metadata = []): bool
{
    // Check if team has sufficient credits
    if (!$this->canUseCredits($team, $credits)) {
        return false;
    }
    
    // Log the usage transaction
    $this->logTransaction($team, $user, [
        'type' => 'usage',
        'credits' => -$credits, // Negative for usage
        'description' => $description ?? 'Credit usage',
        'metadata' => $metadata,
    ]);
    
    // Update team plan usage
    $currentPlan = $team->currentPlan;
    if ($currentPlan) {
        $currentPlan->increment('monthly_usage', $credits);
    }
    
    return true;
}
```

#### Acceptance Criteria
- [ ] Service handles all credit operations correctly
- [ ] Business rules are enforced (sufficient balance, etc.)
- [ ] Real-time balance calculation works
- [ ] All service methods are thoroughly tested

---

### Task 21: Create Usage Service (Shared API/Web)
**Status**: [ ] Not Started  
**Goal**: Unified usage tracking for API and web interfaces

#### Requirements
- [ ] Create UsageService class
- [ ] Implement usage tracking for different actions
- [ ] Create usage tracking middleware
- [ ] Handle both API and web usage
- [ ] Test usage service functionality

#### Files to create
- `app/Services/UsageService.php`
- `app/Http/Middleware/TrackUsage.php`

#### UsageService Methods
```php
public function trackUsage(Team $team, User $user, string $action, int $credits = 1, array $metadata = []): bool
public function trackApiUsage(Team $team, User $user, string $endpoint, array $metadata = []): bool
public function trackWebUsage(Team $team, User $user, string $action, array $metadata = []): bool
public function getActionCreditCost(string $action): int
public function canPerformAction(Team $team, string $action): bool
public function getUsageByAction(Team $team, string $period = '30days'): array
public function getApiUsageStats(Team $team, string $period = '30days'): array
```

#### Usage Tracking Middleware
```php
public function handle(Request $request, Closure $next)
{
    $response = $next($request);
    
    // Only track for authenticated users with teams
    if ($request->user() && $request->user()->currentTeam) {
        $team = $request->user()->currentTeam;
        $user = $request->user();
        
        // Determine action and credit cost
        $action = $this->getActionFromRequest($request);
        $credits = $this->getCreditCostForAction($action);
        
        // Track the usage
        app(UsageService::class)->trackUsage($team, $user, $action, $credits, [
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'endpoint' => $request->path(),
            'method' => $request->method(),
        ]);
    }
    
    return $response;
}
```

#### Acceptance Criteria
- [ ] Usage service tracks all types of usage
- [ ] Middleware automatically tracks API usage
- [ ] Different actions have configurable credit costs
- [ ] Usage statistics are accurate and performant

---

### Task 22: Create Usage Controller (Shared)
**Status**: [ ] Not Started  
**Goal**: Unified usage controller for API and web interfaces

#### Requirements
- [ ] Create UsageController for manual usage tracking
- [ ] Implement usage endpoints for API
- [ ] Add proper validation and error handling
- [ ] Support both JSON and web responses
- [ ] Test usage controller functionality

#### Files to create
- `app/Http/Controllers/UsageController.php`
- `app/Http/Requests/UseCreditsRequest.php`

#### Controller Methods
```php
public function store(UseCreditsRequest $request)
public function index(Request $request) // Usage history
public function stats(Request $request) // Usage statistics
public function balance(Request $request) // Current balance
```

#### UseCreditsRequest Validation
```php
public function rules()
{
    return [
        'action' => 'required|string|max:255',
        'credits' => 'sometimes|integer|min:1|max:100',
        'description' => 'sometimes|string|max:500',
        'metadata' => 'sometimes|array',
    ];
}
```

#### API Responses
```php
// Success response
{
    "success": true,
    "transaction_id": 123,
    "remaining_credits": 850,
    "message": "Usage tracked successfully"
}

// Insufficient credits response
{
    "error": "Insufficient credits",
    "code": "INSUFFICIENT_CREDITS",
    "required": 10,
    "available": 5
}
```

#### Acceptance Criteria
- [ ] Controller handles usage requests properly
- [ ] Proper validation and error responses
- [ ] Both API and web interfaces work
- [ ] All controller methods are tested

---

### Task 23: Create Credit Admin Management
**Status**: [ ] Not Started  
**Goal**: Admin interface for credit management

#### Requirements
- [ ] Create AdminCreditController
- [ ] Implement credit addition/removal for teams
- [ ] Create credit management Livewire components
- [ ] Build admin credit management views
- [ ] Test admin credit management functionality

#### Files to create
- `app/Http/Controllers/Admin/AdminCreditController.php`
- `app/Http/Requests/Admin/AddCreditsRequest.php`
- `app/Http/Requests/Admin/AdjustCreditsRequest.php`
- `app/Livewire/Admin/AdminCreditManager.php`
- `resources/views/admin/credits/index.blade.php`
- `resources/views/livewire/admin/admin-credit-manager.blade.php`

#### AdminCreditController Methods
```php
public function index() // Credit management dashboard
public function addCredits(AddCreditsRequest $request, Team $team)
public function adjustCredits(AdjustCreditsRequest $request, Team $team)
public function refundCredits(Request $request, Team $team)
public function transactionHistory(Team $team)
public function usageReport(Request $request)
```

#### Livewire Component Features
```php
// AdminCreditManager
public $team;
public $credits;
public $description;
public $type = 'additional';

public function addCredits()
public function adjustCredits()
public function refundCredits()
public function viewTransactions()
```

#### Admin UI Features
- Team credit balance overview
- Add/remove credits for teams
- View transaction history
- Usage analytics and reports
- Bulk credit operations
- Export transaction data

#### Acceptance Criteria
- [ ] Admin can manage credits for all teams
- [ ] All credit operations are logged properly
- [ ] UI is intuitive and responsive
- [ ] Proper authorization is implemented

---

### Task 24: Create Test Usage Route
**Status**: [ ] Not Started  
**Goal**: Testing endpoint for credit usage validation

#### Requirements
- [ ] Create /testusage route for development
- [ ] Implement random credit usage simulation
- [ ] Test usage tracking and credit deduction
- [ ] Verify credit balance updates
- [ ] Add proper authentication

#### Files to update
- `routes/web.php`

#### Test Route Implementation
```php
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/testusage', function () {
        if (!auth()->user()->currentTeam) {
            return response()->json(['error' => 'No active team'], 400);
        }
        
        $team = auth()->user()->currentTeam;
        $user = auth()->user();
        $credits = rand(1, 10);
        
        $usageService = app(UsageService::class);
        $result = $usageService->trackUsage($team, $user, 'test_usage', $credits, [
            'test_data' => true,
            'timestamp' => now()->toISOString(),
            'random_value' => rand(1, 1000),
        ]);
        
        if ($result) {
            $balance = app(CreditService::class)->getBalance($team);
            return response()->json([
                'success' => true,
                'credits_used' => $credits,
                'remaining_balance' => $balance,
                'message' => "Used {$credits} credits. Remaining: {$balance}",
            ]);
        } else {
            return response()->json([
                'error' => 'Insufficient credits',
                'available' => app(CreditService::class)->getBalance($team),
            ], 402);
        }
    })->name('test.usage');
});
```

#### Acceptance Criteria
- [ ] Test route works correctly
- [ ] Credit usage is properly tracked
- [ ] Balance updates are accurate
- [ ] Proper error handling for insufficient credits

---

### Task 25: Test Credit System Module
**Status**: [ ] Not Started  
**Goal**: Ensure credit system works correctly

#### Requirements
- [ ] Create credit transaction model tests
- [ ] Create credit service tests
- [ ] Create usage service tests
- [ ] Create controller tests
- [ ] Test complete credit workflow

#### Files to create
- `tests/Unit/Models/CreditTransactionTest.php`
- `tests/Unit/Services/CreditServiceTest.php`
- `tests/Unit/Services/UsageServiceTest.php`
- `tests/Feature/Controllers/UsageControllerTest.php`
- `tests/Feature/Admin/CreditManagementTest.php`
- `tests/Feature/Credit/CreditUsageFlowTest.php`

#### Test Coverage Areas
- CreditTransaction model methods
- Credit service business logic
- Usage service tracking
- Controller request handling
- Admin credit management
- Middleware usage tracking
- Balance calculations
- Transaction history

#### Integration Tests
- Complete credit usage workflow
- Admin credit management flow
- API usage tracking
- Web usage tracking
- Credit balance calculations

#### Performance Tests
- Large transaction history queries
- Real-time balance calculations
- Concurrent credit usage
- Usage statistics generation

#### Acceptance Criteria
- [ ] All tests pass
- [ ] Test coverage >80% for credit-related code
- [ ] Edge cases are covered
- [ ] Performance is acceptable
- [ ] Concurrent usage scenarios work

---

## Module Completion Criteria

### Functional Requirements
- [ ] Credit transactions are properly logged and tracked
- [ ] Credit service handles all operations correctly
- [ ] Usage service tracks API and web usage
- [ ] Admin credit management interface is functional
- [ ] Test usage endpoint works for validation
- [ ] All credit-related tests pass

### Technical Requirements
- [ ] Database migration runs without errors
- [ ] CreditTransaction model works as expected
- [ ] Service layers implement business rules correctly
- [ ] Controllers handle requests properly
- [ ] Middleware tracks usage automatically

### Quality Requirements
- [ ] Code follows Laravel best practices
- [ ] All code is properly tested
- [ ] Performance is optimized for high usage
- [ ] Security is implemented throughout
- [ ] Audit trail is comprehensive

## Windows Development Notes
- Test credit operations thoroughly with different scenarios
- Use Laravel Tinker for testing service methods
- Monitor database performance with transaction queries
- Test concurrent usage scenarios

## Next Module
After completing all tasks in this module, proceed to **Module 5: Admin Dashboard**.

## Git Workflow
```bash
# Start module
git checkout -b module-4-credit-system

# After each task
git add .
git commit -m "Task X: [Description] - [What was accomplished]"

# After module completion
git checkout main
git merge module-4-credit-system
git tag v4.0-module-4
git push origin main --tags
```

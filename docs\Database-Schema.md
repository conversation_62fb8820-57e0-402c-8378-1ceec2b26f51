# Database Schema Documentation

## Overview

The Smart Core SaaS system uses a multi-tenant architecture with team-based organization. This document outlines the complete database schema, relationships, and design decisions.

## Core Tables

### 1. Users Table (Extended)

```sql
-- Extends <PERSON><PERSON>'s default users table
ALTER TABLE users ADD COLUMN type ENUM('system_admin', 'team_user') DEFAULT 'team_user';
ALTER TABLE users ADD COLUMN role ENUM('admin', 'user') DEFAULT 'user';
ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP NULL;
```

**Purpose**: Enhanced user management with system and team-level roles and activity tracking.

**Key Fields**:
- `type`: Determines system access level (system_admin for global access, team_user for team-based access)
- `role`: Determines team-level permissions (admin can manage team, user is regular member)
- `is_active`: Allows disabling users without deletion
- `last_login_at`: Tracks user activity

### 2. Plans Table

```sql
CREATE TABLE plans (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NULL,
    monthly_credits INT NULL, -- Null for one-time plans
    one_time_credits INT NULL, -- For free/trial plans
    billing_cycle ENUM('monthly', 'yearly', 'one_time') DEFAULT 'monthly',
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'LKR',
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    is_trial BOOLEAN DEFAULT FALSE, -- Trial plans
    trial_days INT NULL, -- Trial duration
    max_team_members INT DEFAULT 5,
    max_api_calls_per_minute INT DEFAULT 60,
    max_storage_gb INT DEFAULT 1,
    features JSON NULL,
    limits JSON NULL, -- Additional limits and quotas
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_active_default (is_active, is_default),
    INDEX idx_billing_cycle (billing_cycle, is_active),
    INDEX idx_slug (slug)
);
```

**Purpose**: Defines flexible subscription plans with various billing cycles and credit allocations.

**Key Fields**:
- `monthly_credits`: Credits allocated per month (null for one-time plans)
- `one_time_credits`: One-time credit allocation (for free/trial plans)
- `billing_cycle`: monthly, yearly, or one_time billing
- `is_trial`: Identifies trial plans with time limits
- `trial_days`: Duration of trial period
- `max_team_members`: Team size limit per plan
- `max_api_calls_per_minute`: API rate limiting per plan
- `max_storage_gb`: Storage quota per plan
- `features`: JSON field for plan-specific features
- `limits`: JSON field for additional quotas and restrictions

### 3. Team Plans Table

```sql
CREATE TABLE team_plans (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT UNSIGNED NOT NULL,
    plan_id BIGINT UNSIGNED NOT NULL,
    monthly_credits INT NOT NULL,
    additional_credits INT DEFAULT 0,
    monthly_usage INT DEFAULT 0,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    payment_status ENUM('pending', 'paid', 'failed', 'cancelled') DEFAULT 'pending',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSON NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE CASCADE,
    INDEX idx_team_date (team_id, start_date, end_date),
    INDEX idx_team_active (team_id, is_active)
);
```

**Purpose**: Links teams to plans with monthly cycles and usage tracking.

**Key Fields**:
- `monthly_credits`: Credits from the plan (copied for historical tracking)
- `additional_credits`: Extra credits purchased or granted
- `monthly_usage`: Credits used in this cycle
- `start_date/end_date`: Plan cycle period

### 4. Credit Transactions Table

```sql
CREATE TABLE credit_transactions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    type ENUM('usage', 'additional', 'refund', 'adjustment') NOT NULL,
    credits INT NOT NULL,
    reference_type VARCHAR(255) NULL,
    reference_id BIGINT UNSIGNED NULL,
    description TEXT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_team_created (team_id, created_at),
    INDEX idx_user_created (user_id, created_at),
    INDEX idx_reference (reference_type, reference_id)
);
```

**Purpose**: Detailed logging of all credit transactions for auditing and analytics.

**Key Fields**:
- `type`: Transaction type (usage, additional credits, refunds, adjustments)
- `credits`: Number of credits (positive for additions, negative for usage)
- `reference_type/reference_id`: Links to related models (polymorphic)

### 5. Teams Table (Extended)

```sql
-- Extends Jetstream's teams table
ALTER TABLE teams ADD COLUMN status ENUM('active', 'suspended', 'cancelled') DEFAULT 'active';
ALTER TABLE teams ADD COLUMN settings JSON NULL;
ALTER TABLE teams ADD COLUMN last_activity_at TIMESTAMP NULL;
ALTER TABLE teams ADD COLUMN subscription_status ENUM('active', 'past_due', 'cancelled', 'trialing') DEFAULT 'active';
ALTER TABLE teams ADD COLUMN trial_ends_at TIMESTAMP NULL;
ALTER TABLE teams ADD COLUMN timezone VARCHAR(50) DEFAULT 'UTC';
ALTER TABLE teams ADD COLUMN billing_email VARCHAR(255) NULL;
```

**Purpose**: Enhanced team management with subscription tracking and settings.

**Key Fields**:
- `status`: Team operational status
- `subscription_status`: Billing/subscription status
- `trial_ends_at`: Trial expiration date
- `settings`: Team-specific configuration
- `timezone`: Team timezone for reporting
- `billing_email`: Separate billing contact
- `last_activity_at`: Tracks team activity

### 6. Webhooks Table (Enterprise Feature)

```sql
CREATE TABLE webhooks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    events JSON NOT NULL, -- ['usage.recorded', 'plan.changed', etc.]
    secret VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_triggered_at TIMESTAMP NULL,
    failure_count INT DEFAULT 0,
    max_failures INT DEFAULT 5,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    INDEX idx_team_active (team_id, is_active)
);
```

### 7. API Keys Table (Enhanced API Management)

```sql
CREATE TABLE api_keys (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    permissions JSON NULL, -- Specific API permissions
    rate_limit_per_minute INT NULL, -- Override default rate limit
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_key_hash (key_hash),
    INDEX idx_team_active (team_id, is_active)
);
```

### 8. Usage Quotas Table (Advanced Limits)

```sql
CREATE TABLE usage_quotas (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT UNSIGNED NOT NULL,
    quota_type VARCHAR(100) NOT NULL, -- 'api_calls', 'storage', 'bandwidth'
    limit_value BIGINT NOT NULL,
    used_value BIGINT DEFAULT 0,
    reset_period ENUM('hourly', 'daily', 'monthly') DEFAULT 'monthly',
    last_reset_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    UNIQUE KEY unique_team_quota (team_id, quota_type),
    INDEX idx_team_type (team_id, quota_type)
);
```

## Relationships

### User Relationships
- `User` belongs to many `Teams` (via Jetstream's team_user pivot)
- `User` has many `CreditTransactions`
- User `type` determines system-level access (system_admin vs team_user)
- User `role` determines team-level permissions (admin vs user)

### Team Relationships
- `Team` has many `TeamPlans`
- `Team` has one current `TeamPlan` (active plan)
- `Team` has many `CreditTransactions`
- `Team` belongs to many `Users`

### Plan Relationships
- `Plan` has many `TeamPlans`

### TeamPlan Relationships
- `TeamPlan` belongs to `Team`
- `TeamPlan` belongs to `Plan`

## Indexes and Performance

### Critical Indexes

```sql
-- Team Plans
CREATE INDEX idx_team_plans_active ON team_plans(team_id, is_active, start_date, end_date);
CREATE INDEX idx_team_plans_payment ON team_plans(payment_status, end_date);

-- Credit Transactions
CREATE INDEX idx_credit_transactions_team_date ON credit_transactions(team_id, created_at DESC);
CREATE INDEX idx_credit_transactions_type ON credit_transactions(type, created_at DESC);

-- Users
CREATE INDEX idx_users_type_active ON users(type, is_active);
CREATE INDEX idx_users_last_login ON users(last_login_at DESC);

-- Teams
CREATE INDEX idx_teams_status ON teams(status);
CREATE INDEX idx_teams_activity ON teams(last_activity_at DESC);
```

## Data Integrity Rules

### Business Rules
1. Only one active `TeamPlan` per team at any time
2. Credit transactions must always reference valid team and user
3. System admin users don't need team membership
4. Team users must belong to at least one team
5. Default plan must always exist and be active

### Constraints
```sql
-- Ensure only one default plan
CREATE UNIQUE INDEX idx_plans_default ON plans(is_default) WHERE is_default = TRUE;

-- Ensure positive credits in plans
ALTER TABLE plans ADD CONSTRAINT chk_plans_credits CHECK (monthly_credits >= 0);

-- Ensure valid date ranges in team plans
ALTER TABLE team_plans ADD CONSTRAINT chk_team_plans_dates CHECK (end_date > start_date);
```

## Seeder Data Structure

### Default Plans
```php
[
    ['name' => 'Free', 'monthly_credits' => 100, 'price' => 0, 'is_default' => true],
    ['name' => 'Plus', 'monthly_credits' => 1000, 'price' => 2500],
    ['name' => 'Pro', 'monthly_credits' => 5000, 'price' => 10000],
]
```

### System Admin User
```php
[
    'name' => 'System Administrator',
    'email' => '<EMAIL>',
    'type' => 'system_admin',
    'email_verified_at' => now(),
]
```

## Migration Order

1. `add_type_to_users_table.php`
2. `create_plans_table.php`
3. `create_team_plans_table.php`
4. `create_credit_transactions_table.php`
5. `add_status_to_teams_table.php`

## Backup and Maintenance

### Critical Tables for Backup
- `credit_transactions` (financial data)
- `team_plans` (billing history)
- `teams` (customer data)
- `users` (user accounts)

### Cleanup Procedures
- Archive old credit transactions (>2 years)
- Remove cancelled team plans (>1 year)
- Clean up inactive users (>6 months)

## Performance Considerations

### Query Optimization
- Use eager loading for team-plan relationships
- Implement pagination for transaction lists
- Cache frequently accessed plan data
- Index on commonly filtered columns

### Scaling Strategies
- Partition credit_transactions by date
- Consider read replicas for analytics
- Implement caching for team credit balances
- Use database connection pooling

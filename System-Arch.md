# Smart Core SaaS System Architecture & Implementation Guide

## System Overview

A comprehensive Laravel SaaS foundation with Jetstream, Livewire, and Teams functionality that provides:
- Multi-tenant team-based architecture
- Role-based access control
- Credit-based billing system
- Admin panel for management
- API token authentication
- Usage tracking and analytics

## Architecture Components

### 1. User Management & Roles
- **System Admin**: Global system management
- **Team Admin**: Team management, user creation, account settings
- **Team User**: Basic system usage

### 2. Team & Plan Management
- Teams represent companies/organizations
- Each team has one active plan
- Historical plan tracking with team_plans table
- Automatic plan cycling for monthly credits

### 3. Credit System
- Monthly credit allocation per plan
- Usage tracking and logging
- Additional credits management
- Real-time balance calculation

### 4. Payment Integration
- Sri Lankan gateway integration ( Later)
- Offline payment support
- Admin-controlled payment status

## Database Schema

### Required Migrations

#### 1. Update Users Table
```php
// Add to existing users migration or create new migration
Schema::table('users', function (Blueprint $table) {
    $table->enum('type', ['system_admin', 'team_user'])->default('team_user');
    $table->boolean('is_active')->default(true);
    $table->timestamp('last_login_at')->nullable();
});
```

#### 2. Plans Table
```php
Schema::create('plans', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('slug')->unique();
    $table->text('description')->nullable();
    $table->integer('monthly_credits');
    $table->decimal('price', 10, 2);
    $table->string('currency', 3)->default('LKR');
    $table->boolean('is_active')->default(true);
    $table->boolean('is_default')->default(false);
    $table->json('features')->nullable();
    $table->timestamps();
});
```

#### 3. Team Plans Table
```php
Schema::create('team_plans', function (Blueprint $table) {
    $table->id();
    $table->foreignId('team_id')->constrained()->cascadeOnDelete();
    $table->foreignId('plan_id')->constrained()->cascadeOnDelete();
    $table->integer('monthly_credits');
    $table->integer('additional_credits')->default(0);
    $table->integer('monthly_usage')->default(0);
    $table->decimal('paid_amount', 10, 2)->default(0);
    $table->enum('payment_status', ['pending', 'paid', 'failed', 'cancelled'])->default('pending');
    $table->date('start_date');
    $table->date('end_date');
    $table->boolean('is_active')->default(true);
    $table->json('metadata')->nullable();
    $table->timestamps();
    
    $table->index(['team_id', 'start_date', 'end_date']);
    $table->index(['team_id', 'is_active']);
});
```

#### 4. Credit Transactions Table
```php
Schema::create('credit_transactions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('team_id')->constrained()->cascadeOnDelete();
    $table->foreignId('user_id')->constrained()->cascadeOnDelete();
    $table->enum('type', ['usage', 'additional', 'refund', 'adjustment']);
    $table->integer('credits');
    $table->string('reference_type')->nullable();
    $table->unsignedBigInteger('reference_id')->nullable();
    $table->text('description')->nullable();
    $table->json('metadata')->nullable();
    $table->timestamps();
    
    $table->index(['team_id', 'created_at']);
    $table->index(['user_id', 'created_at']);
    $table->index(['reference_type', 'reference_id']);
});
```

#### 5. Update Teams Table
```php
Schema::table('teams', function (Blueprint $table) {
    $table->enum('status', ['active', 'suspended', 'cancelled'])->default('active');
    $table->json('settings')->nullable();
    $table->timestamp('last_activity_at')->nullable();
});
```

## Models & Relationships

### User Model Updates
```php
// Add to User model
public function isSystemAdmin(): bool
{
    return $this->type === 'system_admin';
}

public function isTeamAdmin(): bool
{
    return $this->currentTeam && $this->hasTeamRole($this->currentTeam, 'admin');
}

public function canManageTeam(): bool
{
    return $this->isSystemAdmin() || $this->isTeamAdmin();
}
```

### Team Model Updates
```php
// Add to Team model
public function plans()
{
    return $this->hasMany(TeamPlan::class);
}

public function currentPlan()
{
    return $this->hasOne(TeamPlan::class)
        ->where('is_active', true)
        ->where('start_date', '<=', now())
        ->where('end_date', '>=', now());
}

public function creditTransactions()
{
    return $this->hasMany(CreditTransaction::class);
}

public function getCurrentCredits(): int
{
    $currentPlan = $this->currentPlan;
    return $currentPlan ? 
        ($currentPlan->monthly_credits + $currentPlan->additional_credits - $currentPlan->monthly_usage) : 
        0;
}
```

## Key Services

### 1. PlanService
```php
class PlanService
{
    public function assignDefaultPlan(Team $team): TeamPlan
    public function createMonthlyPlanCycle(Team $team): TeamPlan
    public function upgradePlan(Team $team, Plan $newPlan): TeamPlan
    public function addAdditionalCredits(Team $team, int $credits, string $description = null): void
}
```

### 2. CreditService
```php
class CreditService
{
    public function useCredits(Team $team, User $user, int $credits, string $description = null): bool
    public function addCredits(Team $team, User $user, int $credits, string $type = 'additional'): void
    public function getBalance(Team $team): int
    public function logTransaction(Team $team, User $user, array $data): CreditTransaction
}
```

### 3. TeamPlanService
```php
class TeamPlanService
{
    public function ensureCurrentPlan(Team $team): TeamPlan
    public function isValidPlan(Team $team): bool
    public function getPlanHistory(Team $team): Collection
    public function updatePaymentStatus(TeamPlan $teamPlan, string $status): void
}
```

## Controllers Structure

### Admin Controllers
- `AdminDashboardController` - Main admin dashboard
- `AdminTeamController` - Team management
- `AdminUserController` - User management
- `AdminPlanController` - Plan management
- `AdminCreditController` - Credit management
- `AdminPaymentController` - Payment management
- `AdminStatsController` - Analytics and reporting

### Team Controllers
- `TeamDashboardController` - Team dashboard
- `TeamUserController` - Team user management
- `TeamSettingsController` - Team settings
- `TeamUsageController` - Usage statistics

### API Controllers
- `ApiAuthController` - Token authentication
- `ApiUsageController` - Credit usage endpoints
- `ApiTeamController` - Team information
- `ApiStatsController` - Usage statistics

## Middleware

### 1. EnsureSystemAdmin
```php
public function handle(Request $request, Closure $next)
{
    if (!$request->user()?->isSystemAdmin()) {
        abort(403, 'System admin access required');
    }
    return $next($request);
}
```

### 2. EnsureTeamAdmin
```php
public function handle(Request $request, Closure $next)
{
    if (!$request->user()?->canManageTeam()) {
        abort(403, 'Team admin access required');
    }
    return $next($request);
}
```

### 3. EnsureValidPlan
```php
public function handle(Request $request, Closure $next)
{
    $team = $request->user()?->currentTeam;
    if (!$team || !app(TeamPlanService::class)->isValidPlan($team)) {
        return response()->json(['error' => 'Invalid or expired plan'], 402);
    }
    return $next($request);
}
```

### 4. TrackApiUsage
```php
public function handle(Request $request, Closure $next)
{
    $response = $next($request);
    
    if ($request->user() && $request->user()->currentTeam) {
        app(CreditService::class)->useCredits(
            $request->user()->currentTeam,
            $request->user(),
            1,
            'API usage: ' . $request->path()
        );
    }
    
    return $response;
}
```

## Livewire Components

### Admin Components
- `AdminTeamsList` - Team management table
- `AdminUsersList` - User management table
- `AdminPlanEditor` - Plan creation/editing
- `AdminCreditManager` - Credit management interface
- `AdminPaymentManager` - Payment status management

### Team Components
- `TeamDashboard` - Team overview
- `TeamUsageChart` - Usage visualization
- `TeamUsersList` - Team members management
- `TeamSettings` - Team configuration

## Routes Structure

### Web Routes
```php
// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->group(function () {
    Route::get('/', AdminDashboardController::class)->name('admin.dashboard');
    Route::resource('teams', AdminTeamController::class);
    Route::resource('users', AdminUserController::class);
    Route::resource('plans', AdminPlanController::class);
    Route::post('teams/{team}/credits', [AdminCreditController::class, 'add']);
    Route::post('teams/{team}/plan', [AdminTeamController::class, 'changePlan']);
});

// Team routes
Route::middleware(['auth', 'team.member'])->prefix('team')->group(function () {
    Route::get('/', TeamDashboardController::class)->name('team.dashboard');
    Route::get('/usage', [TeamUsageController::class, 'index']);
    Route::resource('users', TeamUserController::class)->except(['create', 'store']);
});

// Test route
Route::get('/testusage', function () {
    if (auth()->check() && auth()->user()->currentTeam) {
        app(CreditService::class)->useCredits(
            auth()->user()->currentTeam,
            auth()->user(),
            rand(1, 10),
            'Test usage - ' . now()->format('H:i:s')
        );
        return response()->json(['message' => 'Test usage logged']);
    }
    return response()->json(['error' => 'Not authenticated'], 401);
});
```

### API Routes
```php
Route::middleware(['auth:sanctum', 'ensure.valid.plan', 'track.api.usage'])->group(function () {
    Route::get('/team', [ApiTeamController::class, 'show']);
    Route::get('/usage', [ApiUsageController::class, 'index']);
    Route::post('/usage', [ApiUsageController::class, 'store']);
    Route::get('/stats', [ApiStatsController::class, 'index']);
});
```

## Configuration Updates

### 1. Jetstream Configuration
```php
// config/jetstream.php
'features' => [
    Features::termsAndPrivacyPolicy(),
    Features::profilePhotos(),
    Features::api(),
    Features::teams(['invitations' => true]),
    Features::accountDeletion(),
],

// Disable team creation for regular users
'team_creation' => false,
```

### 2. Sanctum Configuration
```php
// config/sanctum.php
'expiration' => null, // Never expire tokens
'middleware' => [
    'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
    'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
],
```

## Frontend Components

### Landing Page
- Simple, clean design
- Register button only
- Basic feature highlights
- Contact information

### Admin Panel
- Dashboard with key metrics
- Team management interface
- User management tools
- Plan configuration
- Credit management
- Payment tracking
- Analytics and reporting

### Team Panel
- Usage dashboard
- Team member management 
- Settings management
- Usage statistics
- API token management

## Additional Features

### 1. Seeder Data
```php
// Database seeders for:
// - Default plans (Free, Plus, Pro)
// - System admin user
// - Sample team data
// - Test credit transactions
```

### 2. Notifications
```php
// Notification classes for:
// - Low credit warnings
// - Plan expiration alerts
// - Payment reminders
// - Usage limit reached
```

### 3. Jobs
```php
// Background jobs for:
// - Monthly plan cycling
// - Usage report generation
// - Payment processing
// - Data cleanup
```

### 4. API Resources
```php
// API response formatting for:
// - Team information
// - Usage statistics
// - Credit transactions
// - User data
```

## Security Considerations

1. **API Rate Limiting**: Implement rate limiting for API endpoints
2. **Input Validation**: Comprehensive validation for all inputs
3. **CSRF Protection**: Enabled for all web routes
4. **SQL Injection Prevention**: Use Eloquent ORM and prepared statements
5. **XSS Protection**: Sanitize all user inputs
6. **Authorization**: Proper role-based access control

## Performance Optimizations

1. **Database Indexing**: Proper indexes on frequently queried columns
2. **Caching**: Redis/Memcached for frequently accessed data
3. **Query Optimization**: Eager loading for relationships
4. **API Pagination**: Paginate large datasets
5. **Background Jobs**: Queue heavy operations

## Testing Strategy

1. **Unit Tests**: Service classes and models
2. **Feature Tests**: Controllers and API endpoints
3. **Integration Tests**: Complete workflows
4. **Performance Tests**: Load testing for API endpoints
# Development Guide

## Overview

This guide outlines the development workflow, coding standards, and best practices for the Smart Core SaaS system.

## Development Workflow

### Module-by-Module Approach

The project follows a strict modular development approach:

1. **Complete each module fully** before moving to the next
2. **Test each component** immediately after creation
3. **Commit after each module** for clean version control
4. **Update documentation** as changes are made

### Task Management Process

1. **Review the current task** in [Tasks.md](../Tasks.md)
2. **Understand requirements** from architecture documentation
3. **Implement the feature** following coding standards
4. **Write comprehensive tests** for the feature
5. **Update task status** and commit changes
6. **Move to next task** only after current is complete

### Git Workflow

```bash
# Start working on a module
git checkout -b module-1-user-system

# Make changes and commit after each task
git add .
git commit -m "Task 1: Update User Model & Database - Add user types and activity tracking"

# After completing the entire module
git checkout main
git merge module-1-user-system
git tag v1.0-module-1
git push origin main --tags
```

## Coding Standards

### Laravel Best Practices

#### 1. Model Conventions
```php
// Use descriptive method names
public function isSystemAdmin(): bool
public function canManageTeam(): bool
public function getCurrentCredits(): int

// Use scopes for common queries
public function scopeActive($query)
public function scopeSystemAdmins($query)

// Define relationships clearly
public function currentPlan()
{
    return $this->hasOne(TeamPlan::class)
        ->where('is_active', true)
        ->where('start_date', '<=', now())
        ->where('end_date', '>=', now());
}
```

#### 2. Service Classes
```php
// Keep services focused and single-purpose
class CreditService
{
    public function useCredits(Team $team, User $user, int $credits, string $description = null): bool
    {
        // Validate before processing
        if (!$this->canUseCredits($team, $credits)) {
            return false;
        }
        
        // Process transaction
        $this->logTransaction($team, $user, [
            'type' => 'usage',
            'credits' => -$credits,
            'description' => $description,
        ]);
        
        return true;
    }
    
    private function canUseCredits(Team $team, int $credits): bool
    {
        return $this->getBalance($team) >= $credits;
    }
}
```

#### 3. Controller Structure
```php
// Keep controllers thin, delegate to services
class AdminTeamController extends Controller
{
    public function __construct(
        private TeamService $teamService,
        private PlanService $planService
    ) {}
    
    public function store(CreateTeamRequest $request)
    {
        $team = $this->teamService->createTeam($request->validated());
        $this->planService->assignDefaultPlan($team);
        
        return redirect()->route('admin.teams.index')
            ->with('success', 'Team created successfully');
    }
}
```

### Testing Standards

#### 1. Test Structure
```php
// Feature tests for complete workflows
class CreditUsageTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_user_can_use_credits_when_sufficient_balance()
    {
        // Arrange
        $team = Team::factory()->create();
        $user = User::factory()->create();
        $team->users()->attach($user);
        
        // Create team plan with credits
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'monthly_credits' => 100,
            'is_active' => true,
        ]);
        
        // Act
        $result = app(CreditService::class)->useCredits($team, $user, 10, 'Test usage');
        
        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseHas('credit_transactions', [
            'team_id' => $team->id,
            'user_id' => $user->id,
            'type' => 'usage',
            'credits' => -10,
        ]);
    }
}
```

#### 2. Unit Tests for Services
```php
class CreditServiceTest extends TestCase
{
    use RefreshDatabase;
    
    private CreditService $creditService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->creditService = app(CreditService::class);
    }
    
    public function test_get_balance_returns_correct_amount()
    {
        $team = Team::factory()->create();
        
        // Create team plan
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'monthly_credits' => 100,
            'additional_credits' => 50,
            'monthly_usage' => 30,
            'is_active' => true,
        ]);
        
        $balance = $this->creditService->getBalance($team);
        
        $this->assertEquals(120, $balance); // 100 + 50 - 30
    }
}
```

### Database Standards

#### 1. Migration Best Practices
```php
// Use descriptive migration names
// 2024_01_01_000001_add_type_to_users_table.php

public function up()
{
    Schema::table('users', function (Blueprint $table) {
        $table->enum('type', ['system_admin', 'team_user'])
              ->default('team_user')
              ->after('email_verified_at');
        $table->boolean('is_active')
              ->default(true)
              ->after('type');
        $table->timestamp('last_login_at')
              ->nullable()
              ->after('is_active');
    });
}

public function down()
{
    Schema::table('users', function (Blueprint $table) {
        $table->dropColumn(['type', 'is_active', 'last_login_at']);
    });
}
```

#### 2. Factory Definitions
```php
// Use realistic test data
class TeamPlanFactory extends Factory
{
    public function definition()
    {
        return [
            'team_id' => Team::factory(),
            'plan_id' => Plan::factory(),
            'monthly_credits' => $this->faker->numberBetween(100, 5000),
            'additional_credits' => 0,
            'monthly_usage' => 0,
            'paid_amount' => $this->faker->randomFloat(2, 0, 10000),
            'payment_status' => 'paid',
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
            'is_active' => true,
        ];
    }
    
    public function active()
    {
        return $this->state(['is_active' => true]);
    }
    
    public function expired()
    {
        return $this->state([
            'start_date' => now()->subMonth()->startOfMonth(),
            'end_date' => now()->subMonth()->endOfMonth(),
            'is_active' => false,
        ]);
    }
}
```

## File Organization

### Directory Structure
```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Admin/           # Admin-specific controllers
│   │   ├── Api/             # API controllers
│   │   ├── Team/            # Team-specific controllers
│   │   └── UsageController.php  # Shared controllers
│   ├── Middleware/          # Custom middleware
│   ├── Requests/            # Form request validation
│   └── Resources/           # API resources
├── Models/                  # Eloquent models
├── Services/                # Business logic services
├── Livewire/               # Livewire components
├── Jobs/                   # Background jobs
├── Notifications/          # Notification classes
└── Observers/              # Model observers
```

### Naming Conventions

#### Controllers
- `AdminTeamController` - Admin functionality
- `ApiTeamController` - API endpoints
- `TeamDashboardController` - Team-specific features

#### Services
- `CreditService` - Credit management
- `PlanService` - Plan operations
- `TeamPlanService` - Team-plan relationships

#### Models
- Use singular names: `Plan`, `TeamPlan`, `CreditTransaction`
- Clear relationship methods: `currentPlan()`, `creditTransactions()`

#### Tests
- Feature tests: `CreditUsageTest`, `AdminTeamManagementTest`
- Unit tests: `CreditServiceTest`, `PlanServiceTest`

## Error Handling

### Service Layer
```php
class CreditService
{
    public function useCredits(Team $team, User $user, int $credits, string $description = null): bool
    {
        try {
            if (!$this->canUseCredits($team, $credits)) {
                Log::warning('Insufficient credits', [
                    'team_id' => $team->id,
                    'requested' => $credits,
                    'available' => $this->getBalance($team),
                ]);
                return false;
            }
            
            $this->logTransaction($team, $user, [
                'type' => 'usage',
                'credits' => -$credits,
                'description' => $description,
            ]);
            
            return true;
            
        } catch (Exception $e) {
            Log::error('Credit usage failed', [
                'team_id' => $team->id,
                'user_id' => $user->id,
                'credits' => $credits,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }
}
```

### API Error Responses
```php
// In API controllers
public function store(Request $request)
{
    try {
        $result = $this->usageService->trackUsage(
            $request->user()->currentTeam,
            $request->user(),
            $request->input('action')
        );
        
        if (!$result) {
            return response()->json([
                'error' => 'Insufficient credits',
                'code' => 'INSUFFICIENT_CREDITS'
            ], 402);
        }
        
        return response()->json(['success' => true]);
        
    } catch (Exception $e) {
        return response()->json([
            'error' => 'Internal server error',
            'code' => 'INTERNAL_ERROR'
        ], 500);
    }
}
```

## Performance Guidelines

### Database Queries
- Use eager loading: `Team::with('currentPlan', 'users')->get()`
- Implement pagination: `CreditTransaction::paginate(50)`
- Add proper indexes on frequently queried columns

### Caching Strategy
```php
// Cache frequently accessed data
public function getBalance(Team $team): int
{
    return Cache::remember(
        "team_balance_{$team->id}",
        300, // 5 minutes
        fn() => $this->calculateBalance($team)
    );
}
```

### Background Jobs
```php
// Use jobs for heavy operations
class CreateMonthlyPlanCycle implements ShouldQueue
{
    public function handle()
    {
        Team::chunk(100, function ($teams) {
            foreach ($teams as $team) {
                app(TeamPlanService::class)->createMonthlyPlanCycle($team);
            }
        });
    }
}
```

## Security Considerations

### Input Validation
- Use Form Requests for all user input
- Validate API parameters strictly
- Sanitize data before database operations

### Authorization
- Use middleware for route protection
- Implement proper role checking
- Validate team membership for team-specific operations

### API Security
- Rate limiting on API endpoints
- Token-based authentication
- Input validation and sanitization

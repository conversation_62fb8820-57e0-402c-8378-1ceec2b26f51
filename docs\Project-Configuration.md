# Project Configuration Guide

## Environment Setup

### Required Environment Variables

Create a `.env` file with the following configuration:

```env
# Application
APP_NAME="Smart Core"
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://localhost

# Database (Windows MySQL Configuration)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=smart_core
DB_USERNAME=root
DB_PASSWORD=
# For XAMPP users, password is usually empty
# For standalone MySQL, use your configured password

# Cache & Session
CACHE_DRIVER=redis
SESSION_DRIVER=database
SESSION_LIFETIME=120

# Queue
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Sanctum
SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1,127.0.0.1:8000,::1

# Jetstream
JETSTREAM_STACK=livewire

# Smart Core Specific
SMART_CORE_DEFAULT_PLAN=free
SMART_CORE_CREDIT_WARNING_THRESHOLD=10
SMART_CORE_AUTO_PLAN_CYCLING=true
```

## Laravel Configuration Updates

### 1. Jetstream Configuration

Update `config/jetstream.php`:

```php
<?php

use Laravel\Jetstream\Features;

return [
    'stack' => 'livewire',
    
    'middleware' => ['web'],
    
    'features' => [
        Features::termsAndPrivacyPolicy(),
        Features::profilePhotos(),
        Features::api(),
        Features::teams(['invitations' => true]),
        Features::accountDeletion(),
    ],
    
    // Disable team creation for regular users
    'team_creation' => false,
    
    // Custom team model
    'team_model' => App\Models\Team::class,
];
```

### 2. Sanctum Configuration

Update `config/sanctum.php`:

```php
<?php

return [
    'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
        '%s%s',
        'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
        Sanctum::currentApplicationUrlWithPort()
    ))),

    'guard' => ['web'],

    'expiration' => null, // Never expire tokens

    'middleware' => [
        'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
        'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
    ],
];
```

### 3. Custom Configuration File

Create `config/smartcore.php`:

```php
<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Plan
    |--------------------------------------------------------------------------
    |
    | The slug of the default plan to assign to new teams
    |
    */
    'default_plan' => env('SMART_CORE_DEFAULT_PLAN', 'free'),

    /*
    |--------------------------------------------------------------------------
    | Credit Warning Threshold
    |--------------------------------------------------------------------------
    |
    | Send warning notifications when credits fall below this number
    |
    */
    'credit_warning_threshold' => env('SMART_CORE_CREDIT_WARNING_THRESHOLD', 10),

    /*
    |--------------------------------------------------------------------------
    | Auto Plan Cycling
    |--------------------------------------------------------------------------
    |
    | Automatically create new plan cycles at the start of each month
    |
    */
    'auto_plan_cycling' => env('SMART_CORE_AUTO_PLAN_CYCLING', true),

    /*
    |--------------------------------------------------------------------------
    | Plans Configuration
    |--------------------------------------------------------------------------
    |
    | Default plans to be seeded into the database
    |
    */
    'plans' => [
        'free' => [
            'name' => 'Free',
            'slug' => 'free',
            'description' => 'Basic plan for getting started',
            'monthly_credits' => 100,
            'price' => 0,
            'currency' => 'LKR',
            'is_default' => true,
            'features' => [
                'api_access' => true,
                'team_members' => 3,
                'support' => 'email',
            ],
        ],
        'plus' => [
            'name' => 'Plus',
            'slug' => 'plus',
            'description' => 'Enhanced plan for growing teams',
            'monthly_credits' => 1000,
            'price' => 2500,
            'currency' => 'LKR',
            'features' => [
                'api_access' => true,
                'team_members' => 10,
                'support' => 'priority',
                'analytics' => true,
            ],
        ],
        'pro' => [
            'name' => 'Pro',
            'slug' => 'pro',
            'description' => 'Professional plan for large teams',
            'monthly_credits' => 5000,
            'price' => 10000,
            'currency' => 'LKR',
            'features' => [
                'api_access' => true,
                'team_members' => 50,
                'support' => 'phone',
                'analytics' => true,
                'custom_integrations' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Types
    |--------------------------------------------------------------------------
    |
    | Available user types in the system
    |
    */
    'user_types' => [
        'system_admin' => 'System Administrator',
        'team_user' => 'Team User',
    ],

    /*
    |--------------------------------------------------------------------------
    | Team Statuses
    |--------------------------------------------------------------------------
    |
    | Available team statuses
    |
    */
    'team_statuses' => [
        'active' => 'Active',
        'suspended' => 'Suspended',
        'cancelled' => 'Cancelled',
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Statuses
    |--------------------------------------------------------------------------
    |
    | Available payment statuses for team plans
    |
    */
    'payment_statuses' => [
        'pending' => 'Pending',
        'paid' => 'Paid',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled',
    ],

    /*
    |--------------------------------------------------------------------------
    | Credit Transaction Types
    |--------------------------------------------------------------------------
    |
    | Available credit transaction types
    |
    */
    'credit_transaction_types' => [
        'usage' => 'Usage',
        'additional' => 'Additional Credits',
        'refund' => 'Refund',
        'adjustment' => 'Adjustment',
    ],
];
```

## Middleware Registration

Update `app/Http/Kernel.php`:

```php
protected $routeMiddleware = [
    // ... existing middleware
    'admin' => \App\Http\Middleware\EnsureSystemAdmin::class,
    'team.admin' => \App\Http\Middleware\EnsureTeamAdmin::class,
    'valid.plan' => \App\Http\Middleware\EnsureValidPlan::class,
    'track.usage' => \App\Http\Middleware\TrackUsage::class,
];
```

## Service Provider Registration

Create `app/Providers/SmartCoreServiceProvider.php`:

```php
<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\CreditService;
use App\Services\PlanService;
use App\Services\TeamPlanService;
use App\Services\UsageService;

class SmartCoreServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton(CreditService::class);
        $this->app->singleton(PlanService::class);
        $this->app->singleton(TeamPlanService::class);
        $this->app->singleton(UsageService::class);
    }

    public function boot()
    {
        // Register observers
        \App\Models\Team::observe(\App\Observers\TeamObserver::class);
        \App\Models\User::observe(\App\Observers\UserObserver::class);
    }
}
```

Register in `config/app.php`:

```php
'providers' => [
    // ... existing providers
    App\Providers\SmartCoreServiceProvider::class,
],
```

## Database Configuration

### Migration Order

Ensure migrations run in this order by using appropriate timestamps:

1. `2024_01_01_000001_add_type_to_users_table.php`
2. `2024_01_01_000002_create_plans_table.php`
3. `2024_01_01_000003_create_team_plans_table.php`
4. `2024_01_01_000004_create_credit_transactions_table.php`
5. `2024_01_01_000005_add_status_to_teams_table.php`

### Seeder Registration

Update `database/seeders/DatabaseSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        $this->call([
            PlanSeeder::class,
            SystemAdminSeeder::class,
            // TestDataSeeder::class, // Only for development
        ]);
    }
}
```

## Frontend Configuration

### Tailwind CSS Configuration

Update `tailwind.config.js`:

```javascript
module.exports = {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './vendor/laravel/jetstream/**/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                'smart-blue': '#3B82F6',
                'smart-green': '#10B981',
                'smart-red': '#EF4444',
                'smart-yellow': '#F59E0B',
            },
        },
    },

    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/typography'),
    ],
};
```

### Vite Configuration

Update `vite.config.js`:

```javascript
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/css/admin.css',
                'resources/js/admin.js',
                'resources/css/team.css',
                'resources/js/team.js',
            ],
            refresh: true,
        }),
    ],
});
```

## Testing Configuration

### PHPUnit Configuration

Update `phpunit.xml`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
    </coverage>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
```

## Development Tools

### Composer Scripts

Add to `composer.json`:

```json
{
    "scripts": {
        "test": "vendor/bin/phpunit",
        "test-coverage": "vendor/bin/phpunit --coverage-html coverage",
        "pint": "vendor/bin/pint",
        "stan": "vendor/bin/phpstan analyse",
        "fresh": [
            "php artisan migrate:fresh",
            "php artisan db:seed"
        ]
    }
}
```

### Git Hooks

Create `.githooks/pre-commit`:

```bash
#!/bin/sh
# Run tests before commit
vendor/bin/phpunit
if [ $? -ne 0 ]; then
    echo "Tests failed. Commit aborted."
    exit 1
fi

# Run code style check
vendor/bin/pint --test
if [ $? -ne 0 ]; then
    echo "Code style check failed. Run 'composer pint' to fix."
    exit 1
fi
```

## Production Configuration

### Environment Variables for Production

```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Use strong cache drivers
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Enable HTTPS
SESSION_SECURE_COOKIE=true

# Production database
DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_DATABASE=smart_core_prod

# Production mail
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
```

### Optimization Commands

```bash
# Production optimization
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
composer install --optimize-autoloader --no-dev
```

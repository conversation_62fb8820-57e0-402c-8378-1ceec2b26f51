# Module 2: Plans System

## Overview
Create a flexible subscription plans system that defines different service tiers with credit allocations, pricing, and features. This module establishes the foundation for the billing system.

## Module Goals
- Create plans database table and model
- Implement plan service for business logic
- Create plan seeders with default plans (Free, Plus, Pro)
- Build admin plan management interface
- Create comprehensive tests
- Establish plan validation and features

## Tasks

### Task 7: Create Plans Database & Model
**Status**: [ ] Not Started  
**Goal**: Setup plans foundation with database and model

#### Requirements
- [ ] Create plans table migration
- [ ] Create Plan model with relationships and methods
- [ ] Create PlanFactory for testing
- [ ] Test plan model functionality

#### Files to create
- `database/migrations/2024_01_01_000002_create_plans_table.php`
- `app/Models/Plan.php`
- `database/factories/PlanFactory.php`

#### Migration Schema
```php
Schema::create('plans', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('slug')->unique();
    $table->text('description')->nullable();
    $table->integer('monthly_credits')->nullable(); // Null for one-time plans
    $table->integer('one_time_credits')->nullable(); // For free/trial plans
    $table->enum('billing_cycle', ['monthly', 'yearly', 'one_time'])->default('monthly');
    $table->decimal('price', 10, 2);
    $table->string('currency', 3)->default('LKR');
    $table->boolean('is_active')->default(true);
    $table->boolean('is_default')->default(false);
    $table->boolean('is_trial')->default(false);
    $table->integer('trial_days')->nullable();
    $table->integer('max_team_members')->default(5);
    $table->integer('max_api_calls_per_minute')->default(60);
    $table->integer('max_storage_gb')->default(1);
    $table->json('features')->nullable();
    $table->json('limits')->nullable(); // Additional quotas
    $table->timestamps();

    $table->index(['is_active', 'is_default']);
    $table->index(['billing_cycle', 'is_active']);
    $table->index('slug');
});
```

#### Plan Model Methods
```php
public function teamPlans()
public function isDefault(): bool
public function isActive(): bool
public function isTrial(): bool
public function isOneTime(): bool
public function isRecurring(): bool
public function getTotalCredits(): int // monthly_credits or one_time_credits
public function scopeActive($query)
public function scopeDefault($query)
public function scopeByBillingCycle($query, string $cycle)
public function getFeature(string $feature)
public function hasFeature(string $feature): bool
public function getLimit(string $limit)
public function hasLimit(string $limit): bool
```

#### Acceptance Criteria
- [ ] Migration creates table with proper structure
- [ ] Plan model has all required methods
- [ ] Factory creates realistic test data
- [ ] Model relationships work correctly

---

### Task 8: Create Plan Service
**Status**: [ ] Not Started  
**Goal**: Implement plan business logic and operations

#### Requirements
- [ ] Create PlanService class
- [ ] Implement plan assignment logic
- [ ] Implement plan upgrade/downgrade logic
- [ ] Implement plan validation
- [ ] Test plan service functionality

#### Files to create
- `app/Services/PlanService.php`

#### Service Methods
```php
public function getDefaultPlan(): Plan
public function getAllActivePlans(): Collection
public function createPlan(array $data): Plan
public function updatePlan(Plan $plan, array $data): Plan
public function deletePlan(Plan $plan): bool
public function validatePlanData(array $data): array
public function getPlanBySlug(string $slug): ?Plan
```

#### Business Logic
- Ensure only one default plan exists
- Validate plan data before creation/update
- Handle plan feature management
- Manage plan pricing and currency

#### Acceptance Criteria
- [ ] Service handles all plan operations
- [ ] Business rules are enforced
- [ ] Proper validation is implemented
- [ ] All service methods are tested

---

### Task 9: Create Plan Seeder
**Status**: [ ] Not Started  
**Goal**: Setup default plans (Free, Plus, Pro)

#### Requirements
- [ ] Create PlanSeeder with default plans
- [ ] Set Free plan as default
- [ ] Include realistic features for each plan
- [ ] Test plan seeding

#### Files to create
- `database/seeders/PlanSeeder.php`
- Update `database/seeders/DatabaseSeeder.php`

#### Default Plans Structure
```php
// Free Plan (One-time credits)
[
    'name' => 'Free',
    'slug' => 'free',
    'description' => 'Perfect for getting started - 50 credits to try our service',
    'monthly_credits' => null,
    'one_time_credits' => 50,
    'billing_cycle' => 'one_time',
    'price' => 0,
    'is_default' => true,
    'max_team_members' => 3,
    'max_api_calls_per_minute' => 10,
    'max_storage_gb' => 1,
    'features' => [
        'api_access' => true,
        'support' => 'email',
        'analytics' => false,
        'webhooks' => false,
        'custom_integrations' => false,
    ],
    'limits' => [
        'max_projects' => 1,
        'max_exports_per_day' => 5,
    ]
]

// Starter Plan (Monthly recurring)
[
    'name' => 'Starter',
    'slug' => 'starter',
    'description' => 'Great for small teams getting started',
    'monthly_credits' => 500,
    'one_time_credits' => null,
    'billing_cycle' => 'monthly',
    'price' => 1500,
    'max_team_members' => 5,
    'max_api_calls_per_minute' => 30,
    'max_storage_gb' => 5,
    'features' => [
        'api_access' => true,
        'support' => 'email',
        'analytics' => true,
        'webhooks' => false,
        'custom_integrations' => false,
    ],
    'limits' => [
        'max_projects' => 5,
        'max_exports_per_day' => 50,
    ]
]

// Plus Plan (Monthly recurring)
[
    'name' => 'Plus',
    'slug' => 'plus',
    'description' => 'Perfect for growing teams',
    'monthly_credits' => 2000,
    'one_time_credits' => null,
    'billing_cycle' => 'monthly',
    'price' => 4500,
    'max_team_members' => 15,
    'max_api_calls_per_minute' => 100,
    'max_storage_gb' => 20,
    'features' => [
        'api_access' => true,
        'support' => 'priority',
        'analytics' => true,
        'webhooks' => true,
        'custom_integrations' => false,
    ],
    'limits' => [
        'max_projects' => 25,
        'max_exports_per_day' => 200,
    ]
]

// Pro Plan (Monthly recurring)
[
    'name' => 'Pro',
    'slug' => 'pro',
    'description' => 'For professional teams and enterprises',
    'monthly_credits' => 10000,
    'one_time_credits' => null,
    'billing_cycle' => 'monthly',
    'price' => 15000,
    'max_team_members' => 50,
    'max_api_calls_per_minute' => 500,
    'max_storage_gb' => 100,
    'features' => [
        'api_access' => true,
        'support' => 'phone',
        'analytics' => true,
        'webhooks' => true,
        'custom_integrations' => true,
        'white_label' => true,
        'sso' => true,
    ],
    'limits' => [
        'max_projects' => 100,
        'max_exports_per_day' => 1000,
    ]
]

// Enterprise Plan (Yearly)
[
    'name' => 'Enterprise',
    'slug' => 'enterprise',
    'description' => 'Custom solution for large organizations',
    'monthly_credits' => 50000,
    'one_time_credits' => null,
    'billing_cycle' => 'yearly',
    'price' => 150000, // Annual price
    'max_team_members' => 200,
    'max_api_calls_per_minute' => 2000,
    'max_storage_gb' => 500,
    'features' => [
        'api_access' => true,
        'support' => 'dedicated',
        'analytics' => true,
        'webhooks' => true,
        'custom_integrations' => true,
        'white_label' => true,
        'sso' => true,
        'custom_contracts' => true,
        'priority_support' => true,
    ],
    'limits' => [
        'max_projects' => 'unlimited',
        'max_exports_per_day' => 'unlimited',
    ]
]
```

#### Acceptance Criteria
- [ ] Seeder creates all default plans
- [ ] Free plan is set as default
- [ ] Features are properly structured
- [ ] Seeder can run multiple times safely

---

### Task 10: Create Plan Admin Controller
**Status**: [ ] Not Started  
**Goal**: Admin plan management functionality

#### Requirements
- [ ] Create AdminPlanController
- [ ] Implement CRUD operations
- [ ] Add plan validation requests
- [ ] Add proper authorization
- [ ] Test plan admin functionality

#### Files to create
- `app/Http/Controllers/Admin/AdminPlanController.php`
- `app/Http/Requests/Admin/CreatePlanRequest.php`
- `app/Http/Requests/Admin/UpdatePlanRequest.php`

#### Controller Methods
```php
public function index()
public function show(Plan $plan)
public function create()
public function store(CreatePlanRequest $request)
public function edit(Plan $plan)
public function update(UpdatePlanRequest $request, Plan $plan)
public function destroy(Plan $plan)
public function toggleStatus(Plan $plan)
public function setDefault(Plan $plan)
```

#### Validation Rules
```php
// CreatePlanRequest
'name' => 'required|string|max:255',
'slug' => 'required|string|max:255|unique:plans',
'description' => 'nullable|string',
'monthly_credits' => 'required|integer|min:0',
'price' => 'required|numeric|min:0',
'currency' => 'required|string|size:3',
'max_team_members' => 'required|integer|min:1',
'features' => 'nullable|array',
```

#### Acceptance Criteria
- [ ] All CRUD operations work correctly
- [ ] Proper validation is implemented
- [ ] Authorization middleware is applied
- [ ] Error handling is comprehensive

---

### Task 11: Create Plan Admin Views
**Status**: [ ] Not Started  
**Goal**: Admin plan management UI

#### Requirements
- [ ] Create plan admin views
- [ ] Create plan admin Livewire component
- [ ] Style with Tailwind CSS
- [ ] Add interactive features
- [ ] Test plan admin UI

#### Files to create
- `resources/views/admin/plans/index.blade.php`
- `resources/views/admin/plans/create.blade.php`
- `resources/views/admin/plans/edit.blade.php`
- `resources/views/admin/plans/show.blade.php`
- `app/Livewire/Admin/AdminPlansList.php`
- `app/Livewire/Admin/AdminPlanEditor.php`
- `resources/views/livewire/admin/admin-plans-list.blade.php`
- `resources/views/livewire/admin/admin-plan-editor.blade.php`

#### UI Features
- Plans listing with search and filters
- Plan creation and editing forms
- Plan status toggle (active/inactive)
- Default plan management
- Feature management interface
- Bulk operations

#### Livewire Components
```php
// AdminPlansList
public $search = '';
public $filter = 'all';
public function toggleStatus($planId)
public function setDefault($planId)
public function deletePlan($planId)

// AdminPlanEditor
public $plan;
public $features = [];
public function save()
public function addFeature()
public function removeFeature($key)
```

#### Acceptance Criteria
- [ ] All views render correctly
- [ ] Livewire components work properly
- [ ] UI is responsive and user-friendly
- [ ] All interactions work as expected

---

### Task 12: Test Plans Module
**Status**: [ ] Not Started  
**Goal**: Ensure plans system works correctly

#### Requirements
- [ ] Create plan model tests
- [ ] Create plan service tests
- [ ] Create plan controller tests
- [ ] Create plan Livewire tests
- [ ] Test complete plan workflow

#### Files to create
- `tests/Unit/Models/PlanTest.php`
- `tests/Unit/Services/PlanServiceTest.php`
- `tests/Feature/Admin/PlanManagementTest.php`
- `tests/Feature/Livewire/AdminPlansListTest.php`
- `tests/Feature/Livewire/AdminPlanEditorTest.php`

#### Test Coverage Areas
- Plan model methods and relationships
- Plan service business logic
- Admin controller CRUD operations
- Livewire component interactions
- Plan validation and constraints
- Default plan management

#### Performance Tests
- Plan listing with large datasets
- Plan search and filtering
- Feature management operations

#### Acceptance Criteria
- [ ] All tests pass
- [ ] Test coverage >80% for plan-related code
- [ ] Edge cases are covered
- [ ] Performance is acceptable

---

## Module Completion Criteria

### Functional Requirements
- [ ] Plans CRUD operations working correctly
- [ ] Plan service handling business logic properly
- [ ] Default plans seeded and functional
- [ ] Admin plan management interface working
- [ ] Plan features system operational
- [ ] All plan-related tests passing

### Technical Requirements
- [ ] Database migration runs without errors
- [ ] Plan model methods work as expected
- [ ] Service layer implements business rules
- [ ] Controllers handle requests properly
- [ ] Livewire components are interactive

### Quality Requirements
- [ ] Code follows Laravel best practices
- [ ] All code is properly tested
- [ ] UI is responsive and accessible
- [ ] No security vulnerabilities
- [ ] Performance is acceptable

## Windows Development Notes
- Ensure MySQL is running for migrations
- Use `php artisan serve` for testing UI
- Test with different browsers for compatibility
- Use Laravel Debugbar for development debugging

## Next Module
After completing all tasks in this module, proceed to **Module 3: Team Plans System**.

## Git Workflow
```bash
# Start module
git checkout -b module-2-plans-system

# After each task
git add .
git commit -m "Task X: [Description] - [What was accomplished]"

# After module completion
git checkout main
git merge module-2-plans-system
git tag v2.0-module-2
git push origin main --tags
```

# Smart Core SaaS - Quick Reference

## 🚀 **Essential Services & Methods**

### **Credit Management**
```php
// Check balance
$balance = app(CreditService::class)->getBalance($team);

// Use credits
$success = app(CreditService::class)->useCredits($team, $user, 5, 'API call');

// Check if can use credits
$canUse = app(CreditService::class)->canUseCredits($team, 10);

// Add credits (admin)
app(CreditService::class)->addCredits($team, $user, 100, 'additional', 'Bonus credits');
```

### **Usage Tracking**
```php
// Track any usage
$success = app(UsageService::class)->trackUsage($team, $user, 'api_call', 1, [
    'endpoint' => '/api/process',
    'response_time' => 250
]);

// Track API usage automatically
$success = app(UsageService::class)->trackApiUsage($team, $user, '/api/endpoint');
```

### **Plan Management**
```php
// Get current plan
$currentPlan = $team->currentPlan;

// Check plan features
if ($currentPlan->plan->hasFeature('webhooks')) {
    // Feature available
}

// Check plan limits
$limit = $currentPlan->plan->getLimit('max_api_calls_per_minute');

// Get total credits (monthly or one-time)
$totalCredits = $currentPlan->plan->getTotalCredits();
```

### **Team Management**
```php
// Check team status
if ($team->isActive()) {
    // Team is active
}

// Get/set team settings
$timezone = $team->getSetting('timezone', 'UTC');
$team->setSetting('theme.primary_color', '#3B82F6');

// Update last activity
$team->updateLastActivity();
```

### **User Management**
```php
// Check user permissions
if ($user->isSystemAdmin()) {
    // System admin access
}

if ($user->isTeamAdmin()) {
    // Team admin access
}

// Check user status
if ($user->is_active) {
    // User is active
}
```

## 🔌 **API Quick Start**

### **Authentication**
```javascript
// Get API token
const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password',
        device_name: 'My App'
    })
});
const { token } = await response.json();
```

### **Track Usage**
```javascript
// Track usage via API
const response = await fetch('/api/usage', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        action: 'document_processing',
        credits: 5,
        metadata: { file_type: 'pdf' }
    })
});
```

### **Get Team Info**
```javascript
// Get current team information
const response = await fetch('/api/team', {
    headers: { 'Authorization': `Bearer ${token}` }
});
const team = await response.json();
```

## 📊 **Analytics & Reporting**

### **Usage Statistics**
```php
// Get usage stats
$stats = app(AnalyticsService::class)->getUsageStatistics($team, '30days');

// Get revenue stats (admin)
$revenue = app(AnalyticsService::class)->getRevenueStats('monthly');
```

### **Custom Analytics**
```php
// Track custom events
Event::dispatch(new CustomAnalyticsEvent($team, 'feature_used', [
    'feature' => 'advanced_search',
    'user_id' => $user->id
]));
```

## 🔔 **Notifications**

### **Built-in Notifications**
```php
// Low credit warning
$user->notify(new LowCreditWarning($team, $remainingCredits));

// Plan expiring
$user->notify(new PlanExpiring($team, $daysRemaining));

// Welcome user
$user->notify(new WelcomeUser($team));
```

### **Custom Notifications**
```php
// Create custom notification
class CustomNotification extends Notification
{
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Custom Subject')
            ->line('Your custom message here.')
            ->action('Action Button', route('custom.route'));
    }
}

// Send notification
$user->notify(new CustomNotification());
```

## 🔧 **Webhooks**

### **Create Webhook**
```php
$webhook = Webhook::create([
    'team_id' => $team->id,
    'name' => 'Usage Notifications',
    'url' => 'https://your-app.com/webhooks/usage',
    'events' => ['usage.recorded', 'credits.low'],
    'secret' => Str::random(32),
    'is_active' => true,
]);
```

### **Available Events**
- `usage.recorded` - When credits are used
- `credits.low` - When credits fall below threshold
- `plan.changed` - When team changes plans
- `plan.expired` - When plan expires
- `payment.successful` - When payment succeeds
- `payment.failed` - When payment fails

## 🛡️ **Middleware Protection**

### **Route Protection**
```php
// Admin only
Route::middleware(['auth', 'admin'])->group(function () {
    // Admin routes
});

// Team admin only
Route::middleware(['auth', 'team.admin'])->group(function () {
    // Team admin routes
});

// Valid plan required
Route::middleware(['auth', 'valid.plan'])->group(function () {
    // Paid features
});

// Active user required
Route::middleware(['auth', 'active.user'])->group(function () {
    // Active user routes
});
```

## 📋 **Plan Configuration**

### **Plan Types**
```php
// One-time plan (free trial)
[
    'name' => 'Free Trial',
    'monthly_credits' => null,
    'one_time_credits' => 50,
    'billing_cycle' => 'one_time',
    'price' => 0,
]

// Monthly recurring
[
    'name' => 'Pro Monthly',
    'monthly_credits' => 1000,
    'billing_cycle' => 'monthly',
    'price' => 29.99,
]

// Yearly with discount
[
    'name' => 'Pro Yearly',
    'monthly_credits' => 1000,
    'billing_cycle' => 'yearly',
    'price' => 299.99, // 2 months free
]
```

### **Features & Limits**
```php
'features' => [
    'api_access' => true,
    'webhooks' => true,
    'analytics' => true,
    'priority_support' => false,
]

'limits' => [
    'max_team_members' => 10,
    'max_api_calls_per_minute' => 100,
    'max_storage_gb' => 10,
    'max_projects' => 25,
]
```

## 🔄 **Background Jobs**

### **Schedule Jobs**
```php
// In app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    $schedule->job(new CreateMonthlyPlanCycle)->monthlyOn(1, '00:00');
    $schedule->job(new GenerateUsageReport)->monthlyOn(1, '06:00');
    $schedule->job(new CleanupOldData)->weekly();
}
```

### **Dispatch Jobs**
```php
// Dispatch immediately
CreateMonthlyPlanCycle::dispatch($team);

// Dispatch with delay
GenerateUsageReport::dispatch($team)->delay(now()->addMinutes(5));

// Dispatch to specific queue
ProcessPayment::dispatch($payment)->onQueue('payments');
```

## 🎯 **Common Patterns**

### **Check Credits Before Action**
```php
public function performAction(Request $request)
{
    $team = auth()->user()->currentTeam;
    $requiredCredits = 5;
    
    // Check credits
    if (!app(CreditService::class)->canUseCredits($team, $requiredCredits)) {
        return response()->json(['error' => 'Insufficient credits'], 402);
    }
    
    // Perform action
    $result = $this->doSomething($request->validated());
    
    // Track usage
    app(UsageService::class)->trackUsage($team, auth()->user(), 'action_performed', $requiredCredits);
    
    return response()->json($result);
}
```

### **Check Feature Access**
```php
public function advancedFeature(Request $request)
{
    $team = auth()->user()->currentTeam;
    
    // Check if plan includes feature
    if (!$team->currentPlan->plan->hasFeature('advanced_analytics')) {
        return response()->json(['error' => 'Feature not available in your plan'], 403);
    }
    
    // Feature logic here
}
```

### **Check Usage Limits**
```php
public function createProject(Request $request)
{
    $team = auth()->user()->currentTeam;
    $currentProjects = $team->projects()->count();
    $maxProjects = $team->currentPlan->plan->getLimit('max_projects');
    
    if ($currentProjects >= $maxProjects) {
        return response()->json(['error' => 'Project limit reached'], 429);
    }
    
    // Create project
}
```

## 📞 **Quick Help**

### **Documentation Links**
- **Full Implementation Guide**: [Smart-Core-Implementation-Guide.md](./docs/Smart-Core-Implementation-Guide.md)
- **Database Schema**: [Database-Schema.md](./docs/Database-Schema.md)
- **API Documentation**: [API-Documentation.md](./docs/API-Documentation.md)
- **Development Guide**: [Development-Guide.md](./docs/Development-Guide.md)

### **Key Configuration Files**
- `config/smartcore.php` - Main configuration
- `config/sanctum.php` - API authentication
- `.env` - Environment variables

### **Important Directories**
- `app/Services/` - Business logic services
- `app/Http/Controllers/Admin/` - Admin controllers
- `app/Http/Controllers/Api/` - API controllers
- `app/Livewire/` - Interactive components
- `database/migrations/` - Database structure

---

**Keep this reference handy while building your SaaS!** 🚀

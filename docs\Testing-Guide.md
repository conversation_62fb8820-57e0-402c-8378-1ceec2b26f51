# Testing Guide

## Overview

This guide outlines the comprehensive testing strategy for the Smart Core SaaS system, including unit tests, feature tests, integration tests, and performance testing.

## Testing Philosophy

### Testing Pyramid
1. **Unit Tests (70%)**: Fast, isolated tests for individual components
2. **Integration Tests (20%)**: Tests for component interactions
3. **End-to-End Tests (10%)**: Full user workflow tests

### Test-Driven Development (TDD)
- Write tests before implementation
- Red-Green-Refactor cycle
- Maintain high test coverage (>80%)

## Test Structure

### Directory Organization
```
tests/
├── Feature/                 # Feature tests (HTTP requests)
│   ├── Admin/              # Admin functionality tests
│   ├── Api/                # API endpoint tests
│   ├── Auth/               # Authentication tests
│   ├── Team/               # Team functionality tests
│   └── Integration/        # End-to-end workflow tests
├── Unit/                   # Unit tests (isolated components)
│   ├── Models/             # Model tests
│   ├── Services/           # Service class tests
│   └── Observers/          # Observer tests
└── TestCase.php            # Base test class
```

## Unit Testing

### Model Tests

#### User Model Test
```php
<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\User;
use App\Models\Team;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_be_system_admin()
    {
        $user = User::factory()->create(['type' => 'system_admin']);
        
        $this->assertTrue($user->isSystemAdmin());
        $this->assertFalse($user->isTeamAdmin());
    }

    public function test_user_can_be_team_admin()
    {
        $team = Team::factory()->create();
        $user = User::factory()->create(['type' => 'team_user']);
        
        $user->teams()->attach($team, ['role' => 'admin']);
        $user->switchTeam($team);
        
        $this->assertFalse($user->isSystemAdmin());
        $this->assertTrue($user->isTeamAdmin());
        $this->assertTrue($user->canManageTeam());
    }

    public function test_user_scopes_work_correctly()
    {
        User::factory()->create(['type' => 'system_admin']);
        User::factory()->create(['type' => 'team_user']);
        User::factory()->create(['type' => 'team_user']);
        
        $this->assertEquals(1, User::systemAdmins()->count());
        $this->assertEquals(2, User::teamUsers()->count());
    }
}
```

#### Team Model Test
```php
<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\Team;
use App\Models\Plan;
use App\Models\TeamPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TeamTest extends TestCase
{
    use RefreshDatabase;

    public function test_team_has_current_plan_relationship()
    {
        $team = Team::factory()->create();
        $plan = Plan::factory()->create();
        
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'plan_id' => $plan->id,
            'is_active' => true,
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
        ]);
        
        $this->assertNotNull($team->currentPlan);
        $this->assertEquals($plan->id, $team->currentPlan->plan_id);
    }

    public function test_team_calculates_current_credits_correctly()
    {
        $team = Team::factory()->create();
        
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'monthly_credits' => 100,
            'additional_credits' => 50,
            'monthly_usage' => 30,
            'is_active' => true,
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
        ]);
        
        $this->assertEquals(120, $team->getCurrentCredits());
    }
}
```

### Service Tests

#### Credit Service Test
```php
<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Models\Team;
use App\Models\User;
use App\Models\TeamPlan;
use App\Services\CreditService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CreditServiceTest extends TestCase
{
    use RefreshDatabase;

    private CreditService $creditService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->creditService = app(CreditService::class);
    }

    public function test_can_use_credits_when_sufficient_balance()
    {
        $team = Team::factory()->create();
        $user = User::factory()->create();
        
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'monthly_credits' => 100,
            'is_active' => true,
        ]);
        
        $result = $this->creditService->useCredits($team, $user, 10, 'Test usage');
        
        $this->assertTrue($result);
        $this->assertDatabaseHas('credit_transactions', [
            'team_id' => $team->id,
            'user_id' => $user->id,
            'type' => 'usage',
            'credits' => -10,
        ]);
    }

    public function test_cannot_use_credits_when_insufficient_balance()
    {
        $team = Team::factory()->create();
        $user = User::factory()->create();
        
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'monthly_credits' => 5,
            'is_active' => true,
        ]);
        
        $result = $this->creditService->useCredits($team, $user, 10, 'Test usage');
        
        $this->assertFalse($result);
        $this->assertDatabaseMissing('credit_transactions', [
            'team_id' => $team->id,
            'type' => 'usage',
        ]);
    }

    public function test_get_balance_calculates_correctly()
    {
        $team = Team::factory()->create();
        
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'monthly_credits' => 100,
            'additional_credits' => 50,
            'monthly_usage' => 30,
            'is_active' => true,
        ]);
        
        $balance = $this->creditService->getBalance($team);
        
        $this->assertEquals(120, $balance);
    }
}
```

## Feature Testing

### Authentication Tests

```php
<?php

namespace Tests\Feature\Auth;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserTypeTest extends TestCase
{
    use RefreshDatabase;

    public function test_system_admin_can_access_admin_panel()
    {
        $admin = User::factory()->create(['type' => 'system_admin']);
        
        $response = $this->actingAs($admin)->get('/admin');
        
        $response->assertStatus(200);
    }

    public function test_team_user_cannot_access_admin_panel()
    {
        $user = User::factory()->create(['type' => 'team_user']);
        
        $response = $this->actingAs($user)->get('/admin');
        
        $response->assertStatus(403);
    }
}
```

### API Tests

```php
<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\User;
use App\Models\Team;
use App\Models\TeamPlan;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ApiUsageTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_track_usage_with_valid_token()
    {
        $user = User::factory()->create();
        $team = Team::factory()->create();
        $user->teams()->attach($team);
        $user->switchTeam($team);
        
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'monthly_credits' => 100,
            'is_active' => true,
        ]);
        
        Sanctum::actingAs($user);
        
        $response = $this->postJson('/api/usage', [
            'action' => 'document_processing',
            'credits' => 5,
            'metadata' => ['document_type' => 'pdf'],
        ]);
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
                
        $this->assertDatabaseHas('credit_transactions', [
            'team_id' => $team->id,
            'user_id' => $user->id,
            'type' => 'usage',
            'credits' => -5,
        ]);
    }

    public function test_cannot_track_usage_without_sufficient_credits()
    {
        $user = User::factory()->create();
        $team = Team::factory()->create();
        $user->teams()->attach($team);
        $user->switchTeam($team);
        
        TeamPlan::factory()->create([
            'team_id' => $team->id,
            'monthly_credits' => 3,
            'is_active' => true,
        ]);
        
        Sanctum::actingAs($user);
        
        $response = $this->postJson('/api/usage', [
            'action' => 'document_processing',
            'credits' => 5,
        ]);
        
        $response->assertStatus(402)
                ->assertJson(['code' => 'INSUFFICIENT_CREDITS']);
    }
}
```

### Admin Tests

```php
<?php

namespace Tests\Feature\Admin;

use Tests\TestCase;
use App\Models\User;
use App\Models\Team;
use App\Models\Plan;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AdminTeamTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_create_team()
    {
        $admin = User::factory()->create(['type' => 'system_admin']);
        $plan = Plan::factory()->create(['is_default' => true]);
        
        $response = $this->actingAs($admin)->post('/admin/teams', [
            'name' => 'Test Team',
            'owner_email' => '<EMAIL>',
            'owner_name' => 'Team Owner',
        ]);
        
        $response->assertRedirect('/admin/teams');
        
        $this->assertDatabaseHas('teams', ['name' => 'Test Team']);
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    public function test_admin_can_assign_plan_to_team()
    {
        $admin = User::factory()->create(['type' => 'system_admin']);
        $team = Team::factory()->create();
        $plan = Plan::factory()->create();
        
        $response = $this->actingAs($admin)->post("/admin/teams/{$team->id}/plan", [
            'plan_id' => $plan->id,
        ]);
        
        $response->assertRedirect();
        
        $this->assertDatabaseHas('team_plans', [
            'team_id' => $team->id,
            'plan_id' => $plan->id,
            'is_active' => true,
        ]);
    }
}
```

## Integration Testing

### End-to-End Workflow Tests

```php
<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Models\User;
use App\Models\Team;
use App\Models\Plan;
use App\Services\CreditService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CreditUsageFlowTest extends TestCase
{
    use RefreshDatabase;

    public function test_complete_credit_usage_workflow()
    {
        // 1. Create system admin and default plan
        $admin = User::factory()->create(['type' => 'system_admin']);
        $plan = Plan::factory()->create([
            'name' => 'Free',
            'monthly_credits' => 100,
            'is_default' => true,
        ]);
        
        // 2. Admin creates a team
        $this->actingAs($admin);
        $response = $this->post('/admin/teams', [
            'name' => 'Test Company',
            'owner_email' => '<EMAIL>',
            'owner_name' => 'Team Owner',
        ]);
        
        $team = Team::where('name', 'Test Company')->first();
        $owner = User::where('email', '<EMAIL>')->first();
        
        // 3. Verify team has default plan assigned
        $this->assertNotNull($team->currentPlan);
        $this->assertEquals(100, $team->getCurrentCredits());
        
        // 4. Team owner uses credits
        $this->actingAs($owner);
        $creditService = app(CreditService::class);
        
        $result = $creditService->useCredits($team, $owner, 10, 'Test usage');
        $this->assertTrue($result);
        
        // 5. Verify credit balance updated
        $team->refresh();
        $this->assertEquals(90, $team->getCurrentCredits());
        
        // 6. Verify transaction logged
        $this->assertDatabaseHas('credit_transactions', [
            'team_id' => $team->id,
            'user_id' => $owner->id,
            'type' => 'usage',
            'credits' => -10,
        ]);
        
        // 7. Admin adds additional credits
        $this->actingAs($admin);
        $response = $this->post("/admin/teams/{$team->id}/credits", [
            'credits' => 50,
            'description' => 'Bonus credits',
        ]);
        
        // 8. Verify additional credits added
        $team->refresh();
        $this->assertEquals(140, $team->getCurrentCredits());
    }
}
```

## Performance Testing

### Load Testing with Pest

```php
<?php

use App\Models\User;
use App\Models\Team;
use App\Models\TeamPlan;
use App\Services\CreditService;

test('credit service can handle concurrent usage', function () {
    $team = Team::factory()->create();
    $users = User::factory()->count(10)->create();
    
    TeamPlan::factory()->create([
        'team_id' => $team->id,
        'monthly_credits' => 1000,
        'is_active' => true,
    ]);
    
    $creditService = app(CreditService::class);
    
    // Simulate concurrent credit usage
    $promises = [];
    foreach ($users as $user) {
        $promises[] = async(fn() => $creditService->useCredits($team, $user, 1, 'Concurrent test'));
    }
    
    $results = await($promises);
    
    // All operations should succeed
    foreach ($results as $result) {
        expect($result)->toBeTrue();
    }
    
    // Verify final balance
    expect($team->getCurrentCredits())->toBe(990);
});
```

## Test Data Management

### Factories

#### Enhanced User Factory
```php
<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    public function definition()
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'remember_token' => Str::random(10),
            'type' => 'team_user',
            'is_active' => true,
        ];
    }

    public function systemAdmin()
    {
        return $this->state(['type' => 'system_admin']);
    }

    public function inactive()
    {
        return $this->state(['is_active' => false]);
    }
}
```

### Test Seeders

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Team;
use App\Models\Plan;

class TestDataSeeder extends Seeder
{
    public function run()
    {
        // Create plans
        $freePlan = Plan::factory()->create([
            'name' => 'Free',
            'monthly_credits' => 100,
            'is_default' => true,
        ]);
        
        $plusPlan = Plan::factory()->create([
            'name' => 'Plus',
            'monthly_credits' => 1000,
        ]);
        
        // Create system admin
        $admin = User::factory()->systemAdmin()->create([
            'email' => '<EMAIL>',
        ]);
        
        // Create test teams with users
        $teams = Team::factory()->count(3)->create();
        
        foreach ($teams as $team) {
            $owner = User::factory()->create();
            $team->users()->attach($owner, ['role' => 'admin']);
            
            // Assign random plan
            $plan = fake()->boolean() ? $freePlan : $plusPlan;
            TeamPlan::factory()->create([
                'team_id' => $team->id,
                'plan_id' => $plan->id,
            ]);
        }
    }
}
```

## Test Commands

### Running Tests

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run tests with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/Admin/AdminTeamTest.php

# Run tests in parallel
php artisan test --parallel

# Run tests with specific filter
php artisan test --filter=CreditService
```

### Continuous Integration

#### GitHub Actions Example
```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: yes
          MYSQL_DATABASE: smart_core_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
        coverage: xdebug
    
    - name: Install dependencies
      run: composer install --prefer-dist --no-interaction
    
    - name: Copy environment file
      run: cp .env.example .env
    
    - name: Generate application key
      run: php artisan key:generate
    
    - name: Run migrations
      run: php artisan migrate
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: smart_core_test
    
    - name: Run tests
      run: php artisan test --coverage
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: smart_core_test
```

## Best Practices

### 1. Test Organization
- Group related tests in the same file
- Use descriptive test method names
- Follow AAA pattern (Arrange, Act, Assert)

### 2. Test Data
- Use factories for consistent test data
- Clean up after each test (RefreshDatabase)
- Use realistic but minimal data

### 3. Assertions
- Use specific assertions
- Test both positive and negative cases
- Verify side effects (database changes, events)

### 4. Performance
- Keep tests fast and focused
- Use database transactions when possible
- Mock external services

### 5. Maintenance
- Keep tests up to date with code changes
- Remove obsolete tests
- Refactor test code like production code

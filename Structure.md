
## Project Structure

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Admin/
│   │   │   ├── AdminDashboardController.php
│   │   │   ├── AdminTeamController.php
│   │   │   ├── AdminUserController.php
│   │   │   ├── AdminPlanController.php
│   │   │   ├── AdminCreditController.php
│   │   │   ├── AdminPaymentController.php
│   │   │   └── AdminStatsController.php
│   │   ├── Api/
│   │   │   ├── ApiAuthController.php
│   │   │   ├── ApiTeamController.php
│   │   │   └── ApiStatsController.php
│   │   ├── UsageController.php (shared for API and web)
│   │   ├── Team/
│   │   │   ├── TeamDashboardController.php
│   │   │   ├── TeamUserController.php
│   │   │   ├── TeamSettingsController.php
│   │   │   └── TeamUsageController.php
│   │   └── LandingController.php
│   ├── Middleware/
│   │   ├── EnsureSystemAdmin.php
│   │   ├── EnsureTeamAdmin.php
│   │   ├── EnsureValidPlan.php
│   │   └── TrackApiUsage.php
│   ├── Resources/
│   │   ├── TeamResource.php
│   │   ├── UserResource.php
│   │   ├── PlanResource.php
│   │   ├── CreditTransactionResource.php
│   │   └── TeamPlanResource.php
│   └── Requests/
│       ├── Admin/
│       │   ├── CreateTeamRequest.php
│       │   ├── UpdateTeamRequest.php
│       │   ├── CreatePlanRequest.php
│       │   └── AddCreditsRequest.php
│       └── Team/
│           ├── CreateUserRequest.php
│           └── UpdateUserRequest.php
├── Livewire/
│   ├── Admin/
│   │   ├── AdminTeamsList.php
│   │   ├── AdminUsersList.php
│   │   ├── AdminPlanEditor.php
│   │   ├── AdminCreditManager.php
│   │   └── AdminPaymentManager.php
│   └── Team/
│       ├── TeamDashboard.php
│       ├── TeamUsageChart.php
│       ├── TeamUsersList.php
│       └── TeamSettings.php
├── Models/
│   ├── Plan.php
│   ├── TeamPlan.php
│   ├── CreditTransaction.php
│   └── User.php (extend)
├── Services/
│   ├── PlanService.php
│   ├── CreditService.php
│   ├── TeamPlanService.php
│   ├── PaymentService.php
│   └── UsageService.php
├── Jobs/
│   ├── CreateMonthlyPlanCycle.php
│   ├── GenerateUsageReport.php
│   └── ProcessPayment.php
├── Notifications/
│   ├── LowCreditWarning.php
│   ├── PlanExpiring.php
│   └── PaymentReminder.php
└── Observers/
    ├── TeamObserver.php
    └── UserObserver.php

database/
├── migrations/
│   ├── 2024_01_01_000001_create_plans_table.php
│   ├── 2024_01_01_000002_create_team_plans_table.php
│   ├── 2024_01_01_000003_create_credit_transactions_table.php
│   ├── 2024_01_01_000004_add_type_to_users_table.php
│   └── 2024_01_01_000005_add_status_to_teams_table.php
├── seeders/
│   ├── PlanSeeder.php
│   ├── SystemAdminSeeder.php
│   └── TestDataSeeder.php
└── factories/
    ├── PlanFactory.php
    ├── TeamPlanFactory.php
    └── CreditTransactionFactory.php

resources/
├── views/
│   ├── admin/
│   │   ├── dashboard.blade.php
│   │   ├── teams/
│   │   ├── users/
│   │   ├── plans/
│   │   └── layouts/
│   ├── team/
│   │   ├── dashboard.blade.php
│   │   ├── users/
│   │   ├── settings/
│   │   └── usage/
│   ├── landing/
│   │   └── index.blade.php
│   └── livewire/
│       ├── admin/
│       └── team/
└── js/
    ├── admin.js
    ├── team.js
    └── components/

tests/
├── Feature/
│   ├── Admin/
│   ├── Api/
│   ├── Team/
│   └── Services/
└── Unit/
    ├── Models/
    └── Services/
```

# Deployment Guide

## Overview

This guide covers deployment strategies for the Smart Core SaaS system, from development to production environments.

## Environment Types

### 1. Development Environment
- Local development with hot reload
- SQLite database for quick setup
- Debug mode enabled
- All features accessible

### 2. Staging Environment
- Production-like environment for testing
- MySQL database
- Debug mode disabled
- Full feature testing

### 3. Production Environment
- Live system with optimizations
- Secure database configuration
- Performance monitoring
- Backup strategies

## Server Requirements

### Minimum Requirements
- **PHP**: 8.2 or higher
- **Memory**: 512MB RAM
- **Storage**: 1GB available space
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Web Server**: Nginx or Apache
- **SSL Certificate**: Required for production

### Recommended Requirements
- **PHP**: 8.3 with OPcache enabled
- **Memory**: 2GB RAM
- **Storage**: 10GB SSD
- **Database**: MySQL 8.0+ with dedicated server
- **Web Server**: Nginx with HTTP/2
- **CDN**: For static assets
- **Redis**: For caching and sessions

## Deployment Methods

### Method 1: Traditional Server Deployment

#### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.3
sudo add-apt-repository ppa:ondrej/php
sudo apt install php8.3 php8.3-fpm php8.3-mysql php8.3-redis \
    php8.3-curl php8.3-json php8.3-mbstring php8.3-xml \
    php8.3-zip php8.3-gd php8.3-intl

# Install Nginx
sudo apt install nginx

# Install MySQL
sudo apt install mysql-server

# Install Redis
sudo apt install redis-server

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install nodejs
```

#### 2. Application Deployment

```bash
# Clone repository
git clone https://gitlab.com/smartocr/smart-core.git /var/www/smart-core
cd /var/www/smart-core

# Install dependencies
composer install --optimize-autoloader --no-dev
npm install && npm run build

# Set permissions
sudo chown -R www-data:www-data /var/www/smart-core
sudo chmod -R 755 /var/www/smart-core
sudo chmod -R 775 /var/www/smart-core/storage
sudo chmod -R 775 /var/www/smart-core/bootstrap/cache

# Environment setup
cp .env.example .env
php artisan key:generate

# Database setup
php artisan migrate
php artisan db:seed --class=PlanSeeder
php artisan db:seed --class=SystemAdminSeeder

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

#### 3. Nginx Configuration

Create `/etc/nginx/sites-available/smart-core`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    root /var/www/smart-core/public;

    index index.php;

    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/smart-core /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Method 2: Docker Deployment

#### 1. Dockerfile

```dockerfile
FROM php:8.3-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    nginx \
    supervisor

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Install Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Set working directory
WORKDIR /var/www

# Copy application
COPY . /var/www

# Install dependencies
RUN composer install --optimize-autoloader --no-dev
RUN npm install && npm run build

# Set permissions
RUN chown -R www-data:www-data /var/www \
    && chmod -R 755 /var/www \
    && chmod -R 775 /var/www/storage \
    && chmod -R 775 /var/www/bootstrap/cache

# Copy configuration files
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

EXPOSE 80

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

#### 2. Docker Compose

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "80:80"
    environment:
      - APP_ENV=production
      - DB_HOST=db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    volumes:
      - ./storage:/var/www/storage

  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: smart_core
      MYSQL_USER: smart_core
      MYSQL_PASSWORD: secure_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
```

### Method 3: Cloud Platform Deployment

#### Laravel Forge
1. Connect your server to Forge
2. Deploy from Git repository
3. Configure environment variables
4. Set up SSL certificate
5. Configure queue workers

#### DigitalOcean App Platform
1. Connect GitHub/GitLab repository
2. Configure build and run commands
3. Set environment variables
4. Add database and Redis components
5. Configure custom domains

## Database Migration Strategy

### Production Migration Process

```bash
# 1. Backup current database
mysqldump -u username -p smart_core > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Put application in maintenance mode
php artisan down

# 3. Pull latest code
git pull origin main

# 4. Install dependencies
composer install --optimize-autoloader --no-dev

# 5. Run migrations
php artisan migrate --force

# 6. Clear and rebuild caches
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 7. Restart queue workers
php artisan queue:restart

# 8. Bring application back online
php artisan up
```

## Monitoring and Logging

### Application Monitoring

#### 1. Laravel Telescope (Development)
```bash
composer require laravel/telescope --dev
php artisan telescope:install
php artisan migrate
```

#### 2. Error Tracking (Production)
```bash
# Install Sentry
composer require sentry/sentry-laravel

# Configure in config/sentry.php
```

### Server Monitoring

#### 1. Log Rotation
```bash
# Configure logrotate for Laravel logs
sudo nano /etc/logrotate.d/laravel

/var/www/smart-core/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

#### 2. System Monitoring
- **CPU/Memory**: Use htop, top
- **Disk Space**: Monitor with df -h
- **Database**: Monitor slow queries
- **Redis**: Monitor memory usage

## Backup Strategy

### Database Backups

#### Automated Daily Backups
```bash
#!/bin/bash
# /usr/local/bin/backup-database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/database"
DB_NAME="smart_core"

# Create backup directory
mkdir -p $BACKUP_DIR

# Create backup
mysqldump -u backup_user -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/smart_core_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/smart_core_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

# Upload to cloud storage (optional)
# aws s3 cp $BACKUP_DIR/smart_core_$DATE.sql.gz s3://your-backup-bucket/
```

#### Cron Job Setup
```bash
# Add to crontab
0 2 * * * /usr/local/bin/backup-database.sh
```

### File Backups

#### Application Files
```bash
#!/bin/bash
# Backup application files (excluding vendor and node_modules)

tar -czf /backups/app/smart_core_$(date +%Y%m%d).tar.gz \
    --exclude='vendor' \
    --exclude='node_modules' \
    --exclude='storage/logs' \
    /var/www/smart-core
```

## Security Considerations

### 1. Server Security
- Keep system packages updated
- Configure firewall (UFW)
- Disable root SSH login
- Use SSH keys instead of passwords
- Regular security audits

### 2. Application Security
- Keep Laravel and dependencies updated
- Use HTTPS everywhere
- Implement rate limiting
- Validate all inputs
- Use CSRF protection
- Sanitize outputs

### 3. Database Security
- Use strong passwords
- Limit database user permissions
- Enable SSL connections
- Regular security updates
- Monitor for suspicious activity

## Performance Optimization

### 1. PHP Optimization
```ini
; php.ini optimizations
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
opcache.validate_timestamps=0
```

### 2. Database Optimization
- Add proper indexes
- Optimize queries
- Use connection pooling
- Regular maintenance

### 3. Caching Strategy
- Redis for sessions and cache
- CDN for static assets
- Browser caching headers
- Database query caching

## Troubleshooting

### Common Issues

#### 1. Permission Errors
```bash
sudo chown -R www-data:www-data /var/www/smart-core
sudo chmod -R 755 /var/www/smart-core
sudo chmod -R 775 /var/www/smart-core/storage
sudo chmod -R 775 /var/www/smart-core/bootstrap/cache
```

#### 2. Queue Worker Issues
```bash
# Restart queue workers
php artisan queue:restart

# Check queue status
php artisan queue:work --verbose
```

#### 3. Cache Issues
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### Log Locations
- **Application Logs**: `/var/www/smart-core/storage/logs/`
- **Nginx Logs**: `/var/log/nginx/`
- **PHP-FPM Logs**: `/var/log/php8.3-fpm.log`
- **MySQL Logs**: `/var/log/mysql/`
